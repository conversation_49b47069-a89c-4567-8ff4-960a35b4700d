package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.BizType;
import com.facishare.webpage.customer.api.constant.TranslateI18nUtils;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.HomePageLayoutTO;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.config.AppMenuConfig;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.UIPaaSConfig;
import com.facishare.webpage.customer.constant.AppTypeEnum;
import com.facishare.webpage.customer.controller.CustomerMenuAction;
import com.facishare.webpage.customer.controller.model.arg.QueryCustomerMenuListArg;
import com.facishare.webpage.customer.controller.model.arg.customer.*;
import com.facishare.webpage.customer.controller.model.result.customer.*;
import com.facishare.webpage.customer.core.service.impl.I18nServiceImpl;
import com.facishare.webpage.customer.model.CustomerMenu;
import com.facishare.webpage.customer.model.SimpleLinkAppResultDto;
import com.facishare.webpage.customer.service.CustomerMenuService;
import com.facishare.webpage.customer.service.HomePageBaseService;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.constant.CustomMenuType.DASHBOARD;
import static com.facishare.webpage.customer.constant.WebPageConstants.CROSS_PaaS;

/**
 * Created by zhangyu on 2020/11/3
 */
@Controller
@Slf4j
@RequestMapping("/customer")
public class CustomerMenuActionImpl implements CustomerMenuAction {

    @Resource
    private CustomerMenuService customerMenuService;
    @Resource
    private HomePageBaseService homePageBaseService;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Resource
    private UIPaaSConfig uiPaaSConfig;
    @Resource
    private CheckService checkService;
    @Resource
    private AppMenuConfig appMenuConfig;

    @Autowired
    private DefaultTenantConfig defaultTenantConfig;
    @Autowired
    private I18nServiceImpl i18nService;

    @Override
    @RequestMapping(value = "saveCustomerMenu", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SaveCustomerMenuResult saveCustomerMenu(@FSUserInfo UserInfo userInfo,
                                                   @FSClientInfo ClientInfo clientInfo,
                                                   @RequestBody SaveCustomerMenuArg arg) {
        arg.valid();
        if (CollectionUtils.isNotEmpty(arg.getCustomerMenu().getApiNameList())
                && arg.getCustomerMenu().getApiNameList().size() > appMenuConfig.getBiDashboardTabCount()) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        int enterpriseId = userInfo.getEnterpriseId();
        CustomerMenu customerMenu = arg.getCustomerMenu();

        //check same name
        checkService.checkCustomerMenuName(enterpriseId, customerMenu);
        //check menuApiName
        CustomerMenu queryCustomerMenuById = customerMenuService.queryCustomerMenuById(enterpriseId, customerMenu.getMenuApiName(), clientInfo.getLocale());
        if (queryCustomerMenuById != null) {
            throw new WebPageException(InterErrorCode.SAME_CUSTOMER_MENU);
        }
        CustomerMenu saveResult = customerMenuService.saveCustomerMenu(enterpriseId, userInfo.getEmployeeId(), customerMenu);

        SaveCustomerMenuResult result = new SaveCustomerMenuResult();
        if (saveResult != null) {
            result.setSuccess(true);
        }
        return result;
    }

    @Override
    @RequestMapping(value = "updateCustomerMenu", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public UpdateCustomerMenuResult updateCustomerMenu(@FSUserInfo UserInfo userInfo,
                                                       @FSClientInfo ClientInfo clientInfo,
                                                       @RequestBody UpdateCustomerMenuArg arg) {
        arg.valid();

        CustomerMenu customerMenu = arg.getCustomerMenu();
        Integer enterpriseId = userInfo.getEnterpriseId();
        checkService.checkCustomerMenuName(enterpriseId, customerMenu);
        CustomerMenu updateResult = customerMenuService.updateCustomerMenu(enterpriseId, userInfo.getEmployeeId(), arg.getCustomerMenu(), clientInfo.getLocale());
        UpdateCustomerMenuResult result = new UpdateCustomerMenuResult();
        if (updateResult != null) {
            result.setSuccess(true);
        }
        return result;
    }

    @Override
    @RequestMapping(value = "deleteCustomerMenu", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public DeleteCustomerMenuResult deleteCustomerMenu(@FSUserInfo UserInfo userInfo,
                                                       @RequestBody DeleteCustomerMenuArg arg) {
        arg.valid();
        customerMenuService.deleteCustomerMenu(userInfo.getEnterpriseId(), Lists.newArrayList(arg.getMenuApiName()));

        DeleteCustomerMenuResult result = new DeleteCustomerMenuResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    @RequestMapping(value = "getCustomerMenuByApiName", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetCustomerMenuByApiNameResult getCustomerMenuByApiName(@FSUserInfo UserInfo userInfo,
                                                                   @FSClientInfo ClientInfo clientInfo,
                                                                   @RequestBody GetCustomerMenuByApiNameArg arg) {
        arg.valid();
        RequestContextManager.initContextForIsFromManage(true);
        CustomerMenu customerMenu = customerMenuService.queryCustomerMenuById(userInfo.getEnterpriseId(), arg.getMenuApiName(), clientInfo.getLocale());
        GetCustomerMenuByApiNameResult result = new GetCustomerMenuByApiNameResult();
        result.setCustomerMenu(customerMenu);
        return result;
    }

    @Override
    @RequestMapping(value = "getCustomerMenuList", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody   // 管理后台自定义菜单项列表
    public GetCustomerMenuListResult getCustomerMenuList(@FSUserInfo UserInfo userInfo,
                                                         @FSClientInfo ClientInfo clientInfo,
                                                         @RequestBody GetCustomerMenuListArg arg) {
        try{
            RequestContextManager.initContextForIsFromManage(true);
            List<CustomerMenu> customerMenus = customerMenuService.queryCustomerByType(userInfo.getEnterpriseId(),
                    arg.getType(),
                    arg.getApplyType(),
                    null,
                    null,
                    clientInfo.getLocale(),
                    true);
            GetCustomerMenuListResult result = new GetCustomerMenuListResult();
            result.setCustomerMenuList(customerMenus);
            return result;
        } finally {
            RequestContextManager.removeContext();
        }
    }

    @Override
    @RequestMapping(value = "getCustomerLayouts", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody   // 获取自定义页面的api->name 用于自定义菜单项页面展示
    public GetCustomerLayoutsResult getCustomerLayouts(@FSUserInfo UserInfo userInfo,
                                                       @FSClientInfo ClientInfo clientInfo,
                                                       @RequestBody(required = false) GetCustomerMenuListArg arg) {

        String appId = BizType.CUSTOMER.getDefaultAppId();
        int applyType = 0;
        if (null != arg && arg.getApplyType() == 1) {
            applyType = arg.getApplyType();
            //如果ApplyType==1 则appId替换为真正的互联应用id, 因为互联自定义页面创建时直接分配给应用了
            appId = arg.getAppId();
            if(StringUtils.isNotBlank(appId) && appId.equals(defaultTenantConfig.getQudaomenhuAppId())){
                appId = BizType.CROSSCUSTOMER.getDefaultAppId();
            }
        }
        List<HomePageLayoutTO> homePageLayoutTOS = homePageBaseService.queryEnableHomePageLayouts(
                userInfo.getEnterpriseId(), BizType.CUSTOMER.getType(), appId, applyType);
        List<GetCustomerLayoutsResult.CustomerLayout> customerLayouts = GetCustomerLayoutsResult.covertCustomerLayouts(homePageLayoutTOS);
        if (CollectionUtils.isNotEmpty(customerLayouts)) {
            List<String> keys = customerLayouts.stream()
                    .map(x -> TranslateI18nUtils.getWebPageNameKey(x.getLayoutApiName()))
                    .collect(Collectors.toList());
            Map<String, String> transValueMap = i18nService.getOnTimeTransValue(userInfo.getEnterpriseId(), keys,
                    clientInfo.getLocale().toLanguageTag(), false, true); // 菜单项走非实时
            customerLayouts.forEach(x -> {
                x.setName(transValueMap.getOrDefault(TranslateI18nUtils.getWebPageNameKey(x.getLayoutApiName())
                        , x.getName()));
            });
        }
        GetCustomerLayoutsResult result = new GetCustomerLayoutsResult();
        result.setCustomerLayoutList(customerLayouts);
        return result;
    }

    @Override
    @RequestMapping(value = "getCrossApps", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetCrossAppsResult getCrossApps(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo, 
                                          @RequestBody(required = false) GetCrossAppsArg arg) {
        List<SimpleLinkAppResultDto> upSimpleLinkApps = remoteCrossService.getUpSimpleLinkAppDto(userInfo.getEnterpriseId(),clientInfo.getLocale());
        // 黑名单过滤
        List<String> crossCustomerMenuNoSupportApps = uiPaaSConfig.getCrossCustomerMenuNoSupportApps();
        upSimpleLinkApps = upSimpleLinkApps.stream().
                filter(x -> !crossCustomerMenuNoSupportApps.contains(x.getAppId())).
                collect(Collectors.toList());
        // 白名单过滤
        if(Objects.nonNull(arg.getMenuType())){
            Set<String> supportApp = SetUtils.emptyIfNull(
                    MapUtils.emptyIfNull(uiPaaSConfig.getCrossMenuSupportAppMap())
                    .get(arg.getMenuType()));
            upSimpleLinkApps = upSimpleLinkApps.stream()
                    .filter(x -> {
                        if(Objects.isNull(x.getPaaSAppVO())){
                            return true;    // paas表没查到就保存, 保证兼容
                        }
                        return  (x.getPaaSAppVO().getAppType() == AppTypeEnum.LINK_APP.getAppType()
                                                    && supportApp.contains(x.getAppId()))   // 预设互联应用需要显示配置appId
                                || (x.getPaaSAppVO().getAppType() == AppTypeEnum.CUSTOMER_LINK_APP.getAppType()
                                                    && supportApp.contains(CROSS_PaaS));    // 自定义互联应用需要配置 "CROSS_PaaS"
                    })
                    .collect(Collectors.toList());
        }
        // 类型转化
        List<GetCrossAppsResult.CrossApp> crossAppList = upSimpleLinkApps.stream().map(x -> {
                    GetCrossAppsResult.CrossApp crossApp = new GetCrossAppsResult.CrossApp();
                    crossApp.setAppId(x.getAppId());
                    crossApp.setAppName(x.getAppName());
                    return crossApp;
                })
                .filter(x -> StringUtils.isNotBlank(x.getAppName()))
                .collect(Collectors.toList());
        // 补充渠道门户
        if (CollectionUtils.isNotEmpty(crossAppList) && !Objects.equals(DASHBOARD, arg.getMenuType())) {
            GetCrossAppsResult.CrossApp crossApp = new GetCrossAppsResult.CrossApp();
            crossApp.setAppId(DefaultTenantConfig.getQudaomenhuAppId());
            crossApp.setAppName(I18N.text(I18NKey.CHANNEL_PORTAL));
            crossAppList.add(crossApp);
        }

        return GetCrossAppsResult.builder().
                crossAppList(crossAppList).build();
    }

    @Override
    @RequestMapping(value = "queryCustomerMenuItems", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetCustomerMenuListResult queryCustomerMenuItemsByPageTemplateId(@FSUserInfo UserInfo userInfo,
                                                                            @FSClientInfo ClientInfo clientInfo,
                                                                            @RequestBody QueryCustomerMenuListArg arg) {
        arg.valid();
        List<CustomerMenu> customerMenus = customerMenuService.queryCustomerMenuByMenuInfo(userInfo.getEnterpriseId(),
                arg.getPageTemplateId(), arg.getApplyType(), arg.getMenuType(), clientInfo.getLocale());
        return GetCustomerMenuListResult.builder().customerMenuList(customerMenus).build();
    }
}
