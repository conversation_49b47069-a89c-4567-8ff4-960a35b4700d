package com.facishare.webpage.customer.controller.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSOuterUserInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.OuterUserInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.converter.EIEAConverter;
import com.facishare.qixin.permission.ValidateFunctionPermission;
import com.facishare.webpage.customer.api.constant.SourceType;
import com.facishare.webpage.customer.api.model.MainChannelMenuVO;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.core.WebMainChannelMenuVO;
import com.facishare.webpage.customer.api.model.result.GetTenantBrandColorResult;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.config.DefaultTenantConfig;
import com.facishare.webpage.customer.config.WebMainChannelConfig;
import com.facishare.webpage.customer.constant.TenantConfigKey;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.controller.TenantWebMainChannelAction;
import com.facishare.webpage.customer.controller.model.ManageWebMainChannelVO;
import com.facishare.webpage.customer.controller.model.arg.paas.*;
import com.facishare.webpage.customer.controller.model.result.paas.*;
import com.facishare.webpage.customer.event.WebMainChannelEventService;
import com.facishare.webpage.customer.model.AppGlobalSettings;
import com.facishare.webpage.customer.model.TenantMainChannelAO;
import com.facishare.webpage.customer.model.WebGlobalSettings;
import com.facishare.webpage.customer.service.WebMainChannelService;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhangyu on 2020/11/16
 */
@Controller
@Slf4j
@RequestMapping("/mainChannelManager")
public class TenantWebMainChannelActionImpl implements TenantWebMainChannelAction {

    @Resource
    private WebMainChannelService webMainChannelService;
    @Resource
    private WebMainChannelConfig webMainChannelConfig;
    @Resource
    private WebMainChannelEventService webMainChannelEventService;
    @Resource
    private CheckService checkService;
    @Resource
    private DefaultTenantConfig defaultTenantConfig;
    @Resource
    private EIEAConverter eieaConverter;

    private List<String> appClientTypes = Lists.newArrayList("Android", "iOS");

    /**
     * 备注：全网，getManWebMainChannelMenu去掉
     *
     * @param userInfo   身份信息
     * @param clientInfo 端的信息
     * @param arg        入参
     * @return
     */
    @Override
    @RequestMapping(value = {"/getManWebMainChannelList", "/getManWebMainChannelMenu"}, method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(functionCode = {"paasappsetting/=/mainnav"})
    public GetManMainChannelListResult getManWebMainChannelList(@FSUserInfo UserInfo userInfo,
                                                                @FSClientInfo ClientInfo clientInfo,
                                                                @RequestBody(required = false) GetManMainChannelListArg arg) {
        try {
            RequestContextManager.initContextForIsFromManage(true);
            List<ManageWebMainChannelVO> tenantMainChannelMenuVOList = webMainChannelService.getTenantMainChannelMenuVOList(
                    userInfo.getEnterpriseId(), userInfo.getEnterpriseAccount(), userInfo.getEmployeeId(), clientInfo.getLocale(), true, arg == null ? null : arg.getScopes());
            GetManMainChannelListResult result = new GetManMainChannelListResult();
            result.setManageWebMainChannelVOS(tenantMainChannelMenuVOList);
            return result;
        } finally {
            RequestContextManager.removeContext();
        }
    }

    @Override
    @RequestMapping(value = "/saveManWebMainChannelMenu", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(functionCode = {"paasappsetting/=/mainnav"})
    public SaveTenantMainChannelResult saveManWebMainChannelMenu(@FSUserInfo UserInfo userInfo,
                                                                 @FSClientInfo ClientInfo clientInfo,
                                                                 @RequestBody SaveTenantMainChannelArg arg) {
        arg.valid();
        Integer enterpriseId = userInfo.getEnterpriseId();
        List<WebMainChannelMenuVO> mainChannelMenuVOList = arg.getMainChannelMenuVOList();
        checkService.checkWebMainChannelMenuLimit(enterpriseId, mainChannelMenuVOList);
        List<Scope> scopeList = arg.getScopeList();
        Boolean showAppName = arg.getShowAppName();
        Boolean canCustom = arg.getCanCustom();
        Boolean showMoreAppEntry = arg.getShowMoreAppEntry();
        String apiName = arg.getApiName();
        TenantMainChannelAO tenantMainChannelAO = TenantMainChannelAO.builder().
                apiName(apiName).
                name(arg.getName()).
                scopeList(scopeList).
                priorityLevel(arg.getPriorityLevel()).
                webMainChannelMenuVOList(mainChannelMenuVOList).
                version(arg.getVersion()).
                showAppName(showAppName).
                canCustom(canCustom).
                showMoreAppEntry(showMoreAppEntry).
                sourceType(SourceType.CUSTOMER).
                build();

        boolean success = webMainChannelService.saveTenantMainChannelMenus(enterpriseId,
                userInfo.getEmployeeId(),
                tenantMainChannelAO, clientInfo.getLocale().toLanguageTag());
        //做埋点
        webMainChannelEventService.createTenantWebMainChannel(userInfo,
                clientInfo,
                scopeList,
                mainChannelMenuVOList,
                showAppName,
                canCustom,
                showMoreAppEntry,
                StringUtils.isEmpty(apiName));
        SaveTenantMainChannelResult result = new SaveTenantMainChannelResult();
        result.setSuccess(success);
        return result;
    }

    @Override
    @RequestMapping(value = "/getManMainChannelByApiName", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(functionCode = {"paasappsetting/=/mainnav"})
    public GetManMainChannelByApiNameResult getManMainChannelByApiName(@FSUserInfo UserInfo userInfo,
                                                                       @FSClientInfo ClientInfo clientInfo,
                                                                       @RequestBody GetManMainChannelByApiNameArg arg) {
        try {
            arg.valid();
            RequestContextManager.initContextForIsFromManage(true);
            ManageWebMainChannelVO mainChannelMenuVO = webMainChannelService.getTenantMainChannelMenuVO(userInfo.getEnterpriseId(),
                    userInfo.getEnterpriseAccount(),
                    userInfo.getEmployeeId(),
                    arg.getApiName(),
                    clientInfo.getLocale());
            GetManMainChannelByApiNameResult result = new GetManMainChannelByApiNameResult();
            result.setManageWebMainChannelVO(mainChannelMenuVO);
            return result;
        } finally {
            RequestContextManager.removeContext();
        }

    }

    @Override
    @RequestMapping(value = "/deleteWebMainChannel", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(functionCode = {"paasappsetting/=/mainnav"})
    public DeleteWebMainChannelResult deleteWebMainChannel(@FSUserInfo UserInfo userInfo,
                                                           @RequestBody DeleteWebMainChannelArg arg) {
        arg.valid();
        webMainChannelService.deleteTenantWebMainChannel(userInfo.getEnterpriseId(), arg.getApiName());
        DeleteWebMainChannelResult result = new DeleteWebMainChannelResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    @RequestMapping(value = "/resetWebMainChannel", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(functionCode = {"paasappsetting/=/mainnav"})
    public ResetWebMainChannelResult resetWebMainChannel(@FSUserInfo UserInfo userInfo,
                                                         @FSClientInfo ClientInfo clientInfo) {
        List<MainChannelMenuVO> mainChannelMenuVOS = webMainChannelService.querySystemMainChannelMenuVOList(userInfo.getEnterpriseId(),
                userInfo.getEnterpriseAccount(),
                userInfo.getEmployeeId(),
                clientInfo.getLocale());
        ResetWebMainChannelResult result = new ResetWebMainChannelResult();
        result.setMainChannelMenuVOList(mainChannelMenuVOS);
        return result;
    }

    @Override
    @RequestMapping(value = "/getManMainChannelMenus", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @ValidateFunctionPermission(functionCode = {"paasappsetting/=/mainnav"})
    public GetManMainChannelMenusResult getManMainChannelMenus(@FSUserInfo UserInfo userInfo,
                                                               @FSClientInfo ClientInfo clientInfo) {
        List<MainChannelMenuVO> mainChannelMenuVOS = webMainChannelService.queryMainChannelMenuVOList(userInfo.getEnterpriseId(),
                userInfo.getEnterpriseAccount(),
                userInfo.getEmployeeId(),
                clientInfo.getLocale());
        GetManMainChannelMenusResult result = new GetManMainChannelMenusResult();
        result.setMainChannelMenuVOList(mainChannelMenuVOS);
        return result;
    }

    @Override
    @RequestMapping(value = "/setTenantBrandColor", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SetTenantBrandColorResult setTenantBrandColor(@FSUserInfo UserInfo userInfo,
                                                         @FSClientInfo ClientInfo clientInfo,
                                                         @RequestBody SetTenantBrandColorArg arg) {
        SetTenantBrandColorResult ret = new SetTenantBrandColorResult();
        webMainChannelService.setTenantConfig(userInfo.getEnterpriseId(), arg.getBrandColor(), TenantConfigKey.TenantBrandColorJson);
        ret.setBrandColor(arg.getBrandColor());
        return ret;
    }

    @Override
    @RequestMapping(value = "/getTenantBrandColor", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetTenantBrandColorResult getTenantBrandColor(@FSUserInfo UserInfo userInfo,
                                                         @FSOuterUserInfo OuterUserInfo outerUserInfo,
                                                         @FSClientInfo ClientInfo clientInfo) {
        GetTenantBrandColorResult ret = new GetTenantBrandColorResult();
        String themeColor = webMainChannelService.getTenantConfig(getTenantId(userInfo),
                TenantConfigKey.TenantBrandColorJson);
        if (StringUtils.isBlank(themeColor)) {
            //配置文件不配置会下发null；
            ret.setBrandColor(defaultTenantConfig.getDefaultTenantBrandColor());
        } else {
            try {
                JSONObject.parseObject(themeColor);
                ret.setBrandColor(themeColor);
            } catch (Exception e) {
                log.error("TenantBrandColorJson convert to JSONObject error, tenantId:{}", userInfo.getEnterpriseId(), e);
                ret.setBrandColor(defaultTenantConfig.getDefaultTenantBrandColor());
            }
        }
        return ret;
    }

    @Override
    @RequestMapping(value = "/createOrUpdateGlobalSetting", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public SaveGlobalSettingResult createOrUpdateGlobalSetting(@FSUserInfo UserInfo userInfo,
                                                               @RequestBody SaveGlobalSettingArg arg) {
        String globalSetting = JSONObject.toJSONString(arg);
        webMainChannelService.setTenantConfig(userInfo.getEnterpriseId(), globalSetting,
                TenantConfigKey.TenantGlobalSettings);
        return SaveGlobalSettingResult.builder().success(true).build();
    }

    @Override
    @RequestMapping(value = "/getGlobalSetting", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetGlobalSettingResult getGlobalSetting(@FSUserInfo UserInfo userInfo, @FSClientInfo ClientInfo clientInfo,
                                                   @RequestBody GetGlobalSettingArg arg) {
        String globalSetting = webMainChannelService.getTenantConfig(getTenantId(userInfo),
                TenantConfigKey.TenantGlobalSettings);
        GetGlobalSettingResult result;
        if (globalSetting == null) {
            result = GetGlobalSettingResult.builder().appGlobalSettings(new AppGlobalSettings()).
                    webGlobalSettings(new WebGlobalSettings()).build();
            if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.hiddenHelpCenter, userInfo.getEnterpriseId())) {
                result.getWebGlobalSettings().setShowWebHelpCenter(false);
                result.getAppGlobalSettings().setShowAppHelpCenter(false);
            }
        } else {
            try {
                result = JSONObject.parseObject(globalSetting, GetGlobalSettingResult.class);
            } catch (Exception e) {
                log.error("getGlobalSetting error", e);
                result = GetGlobalSettingResult.builder().appGlobalSettings(new AppGlobalSettings()).
                        webGlobalSettings(new WebGlobalSettings()).build();
            }
        }
        if (arg.isAllGlobalSettings()) {
            return result;
        }
        if (appClientTypes.contains(clientInfo.getType().getValue())) {
            result.setWebGlobalSettings(null);
        } else {
            result.setAppGlobalSettings(null);
        }
        return result;
    }

    @RequestMapping(value = "/rest/getTenantBrandColor", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    public GetTenantBrandColorResult getTenantBrandColor(@RequestHeader("x-fs-ei") String tenantId) {
        GetTenantBrandColorResult ret = new GetTenantBrandColorResult();
        String themeColor = webMainChannelService.getTenantConfig(Integer.parseInt(tenantId), TenantConfigKey.TenantBrandColorJson);
        if (StringUtils.isBlank(themeColor)) {
            //配置文件不配置会下发null；
            ret.setBrandColor(defaultTenantConfig.getDefaultTenantBrandColor());
        } else {
            ret.setBrandColor(themeColor);
        }
        return ret;
    }

    private int getTenantId(UserInfo userInfo) {
        if (null != userInfo.getEnterpriseId()) {
            return userInfo.getEnterpriseId();
        } else if (StringUtils.isNotEmpty(userInfo.getEnterpriseAccount())) {
            return eieaConverter.enterpriseAccountToId(userInfo.getEnterpriseAccount());
        } else {
            log.error("getTenantId error, userInfo:{}", userInfo);
            return 0;
        }
    }

}
