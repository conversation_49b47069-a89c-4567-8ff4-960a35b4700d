package com.facishare.webpage.customer.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.facishare.webpage.customer.api.model.RoleInfo;
import com.facishare.webpage.customer.api.model.arg.GetlinkAppListArg;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.constant.ComponentConstant;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.service.I18nService;
import com.facishare.webpage.customer.core.service.UIPaasLicenseService;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.model.CrossAppVO;
import com.facishare.webpage.customer.model.SimpleLinkAppResultDto;
import com.facishare.webpage.customer.service.PaaSAppService;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.data.LinkAppPartialData;
import com.fxiaoke.enterpriserelation2.data.UpstreamOpenedLinkAppPartialData;
import com.fxiaoke.enterpriserelation2.result.LinkAppVo;
import com.fxiaoke.enterpriserelation2.result.ListEmployeeAppRolesByUpstreamEaResult;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.fxiaoke.enterpriserelation2.result.data.LinkAppData;
import com.fxiaoke.enterpriserelation2.result.data.OuterTenantSimpleData;
import com.fxiaoke.enterpriserelation2.result.data.RoleInfoData;
import com.fxiaoke.enterpriserelation2.result.data.UserRoleInfoData;
import com.fxiaoke.enterpriserelation2.service.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.constant.TranslateI18nUtils.getAppNametranslateKey;

/**
 * Created by zhangyu on 2020/6/30
 */
@Component
@Slf4j
public class RemoteCrossServiceImpl implements RemoteCrossService {

    private static final Logger logger = LoggerFactory.getLogger(RemoteCrossServiceImpl.class);
    private static final String allOuterRoleId = "allOuterRole";

    private static final RoleInfo allRoleInfo = new RoleInfo("allOuterRole", "所有角色"); //ignoreI18n

    @Resource
    private UpstreamService upstreamService;
    @Resource
    private DownstreamService downstreamService;
    @Resource
    private EnterpriseRelationService enterpriseRelationService;
    @Resource
    private AppOuterRoleService appOuterRoleService;
    @Autowired
    private LinkAppService linkAppService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private I18nService i18nService;
    @Resource
    private UIPaasLicenseService uiPaasLicenseService;
    @Autowired
    private PaaSAppService paaSAppService;

    @Override
    public List<SimpleLinkAppResultDto> getUpSimpleLinkAppDto(int tenantId, Locale locale){
        return getUpSimpleLinkAppInternal(tenantId, locale);
    }

    @Override
    public List<SimpleLinkAppResult> getUpSimpleLinkApp(int tenantId, Locale locale) {
        List<SimpleLinkAppResultDto> dtoList = getUpSimpleLinkAppInternal(tenantId, locale);
        // 创建纯的SimpleLinkAppResult对象，避免类型风险
        return dtoList.stream()
                .filter(Objects::nonNull)
                .map(SimpleLinkAppResultDto::convertToSimpleLinkAppResult)
                .collect(Collectors.toList());
    }

    /**
     * 内部通用方法，处理所有业务逻辑，返回最完整的DTO
     */
    private List<SimpleLinkAppResultDto> getUpSimpleLinkAppInternal(int tenantId, Locale locale) {
        try {
            ListOpenedAppsOutArg arg = new ListOpenedAppsOutArg();
            arg.setUpstreamTenantId(tenantId);
            arg.setAppType(1);

            HeaderObj headerObj = HeaderObj.newInstance(tenantId);
            headerObj.setAppId(WebPageConstants.CROSS_APPID);

            RestResult<List<SimpleLinkAppResult>> result = upstreamService.listOpenedApps(headerObj, arg);
            List<SimpleLinkAppResult> simpleLinkAppResults =
                    result.getData().stream().filter(simpleLinkAppResult -> !simpleLinkAppResult.getIsExprired()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(simpleLinkAppResults)) {
                return Lists.newArrayList();
            }

            // 处理国际化
            if (uiPaasLicenseService.existMultiLanguageModule(tenantId)) {
                for (SimpleLinkAppResult linkAppResult : simpleLinkAppResults) {
                    String key = getAppNametranslateKey(linkAppResult.getAppId());
                    linkAppResult.setAppName(i18nService.getCurrentI18nValue(tenantId, key, linkAppResult.getAppName(),
                            false, Objects.isNull(locale) ? Locale.CHINA.toLanguageTag() : locale.toLanguageTag(), false));
                }
            }

            // 获取PaaSApp信息
            GetlinkAppListArg getlinkAppListArg = new GetlinkAppListArg();
            getlinkAppListArg.setTenantId(tenantId);
            List<PaaSAppVO> paaSAppVOS = paaSAppService.getLinkAppVOList(getlinkAppListArg, locale);
            Map<String, PaaSAppVO> paaSAppVOMap = paaSAppVOS.stream().collect(Collectors.toMap(PaaSAppVO::getAppId, x -> x, (x, y) -> x));

            // 构建扩展DTO列表
            return simpleLinkAppResults.stream().map(simpleLinkApp -> {
                PaaSAppVO paaSAppVO = paaSAppVOMap.get(simpleLinkApp.getAppId());
                if (paaSAppVO != null) {
                    // 更新原有对象的name和icon
                    simpleLinkApp.setAppName(paaSAppVO.getName());
                    simpleLinkApp.setIcon(paaSAppVO.getIcon());
                }
                return SimpleLinkAppResultDto.from(simpleLinkApp, paaSAppVO);
            }).collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("getUpSimpleLinkAppInternal error by tenantId:{}", tenantId, e);
        }

        return Lists.newArrayList();
    }

    @Override
    public List<SimpleLinkAppResult> getUpSimpleLinkApp(int tenantId) {
        return getUpSimpleLinkApp(tenantId, Locale.CHINA);
    }

    @Override
    public Map<String, CrossAppVO> getCrossAppMap(int upTenantId, String upEnterpriseAccount, long outTenantId, long outUserId) {
        List<LinkAppVo> linkAppVo = getLinkAppVo(upTenantId, upEnterpriseAccount, outTenantId, outUserId, null);
        return buildCrossAppMap(linkAppVo);
    }

    @Override
    public List<Integer> getUpTenantIds(int downTenantId) {
        ListUpstreamByOuterTenantIdOutArg arg = new ListUpstreamByOuterTenantIdOutArg();
        arg.setTenantIds(Lists.newArrayList(downTenantId));
        HeaderObj headerObj = HeaderObj.newInstance(0);
        headerObj.setAppId(WebPageConstants.CROSS_APPID);
        List<Integer> tenantIds = Lists.newArrayList();
        try {
            RestResult<Map<Integer, List<OuterTenantSimpleData>>> mapRestResult =
                    enterpriseRelationService.batchListUpstreamByTenantIds(headerObj, arg);
            if (mapRestResult == null || mapRestResult.getData() == null) {
                return tenantIds;
            }
            Map<Integer, List<OuterTenantSimpleData>> data = mapRestResult.getData();
            List<OuterTenantSimpleData> outerTenantSimpleData = data.get(downTenantId);
            for (OuterTenantSimpleData outerTenantSimple : outerTenantSimpleData) {
                if (outerTenantSimple.getTenantId() != null) {
                    tenantIds.add(outerTenantSimple.getTenantId());
                }
            }
        } catch (Exception e) {
            logger.error("batchListUpstreamByTenantIds error", e);
        }
        return tenantIds;
    }

    @Override
    public List<RoleInfo> getOuterRoleInfosByAppId(int tenantId, String appId) {

        try {
            HeaderObj headerObj = HeaderObj.newInstance(tenantId);
            headerObj.setAppId(WebPageConstants.CROSS_APPID);

            ListAppOuterRolesByAppIdArg arg = new ListAppOuterRolesByAppIdArg();
            arg.setLinkAppId(appId);
            arg.setTenantId(tenantId);
            RestResult<List<RoleInfoData>> result = appOuterRoleService.listAppOuterRolesByAppId(headerObj, arg);
            List<RoleInfoData> roleInfoDataList = result.getData();
            if (CollectionUtils.isEmpty(roleInfoDataList)) {
                return Lists.newArrayList();
            }
            List<RoleInfo> roleInfos = sortRoleInfoDataList(roleInfoDataList);
            if (WebPageGraySwitch.isAllowByBusiness(appId, tenantId)) {
                roleInfos.add(new RoleInfo(allOuterRoleId, I18N.text(I18NKey.TRANSLATE_ALL_CHARACTERS)));
            }
            return roleInfos;
        } catch (Exception e) {
            logger.error("getOuterRoleInfosByAppId error by tenantId :{}, appId :{}", tenantId, appId, e);
            return Lists.newArrayList();
        }
    }

    private List<RoleInfo> sortRoleInfoDataList(List<RoleInfoData> roleInfoDataList) {
        if (CollectionUtils.isEmpty(roleInfoDataList)) {
            return Lists.newArrayList();
        }

        Set<String> appIds = roleInfoDataList.stream().
                filter(roleInfoData -> StringUtils.isNotEmpty(roleInfoData.getAppId())).
                map(roleInfoData -> roleInfoData.getAppId()).
                collect(Collectors.toSet());
        List<RoleInfo> roleInfoList = Lists.newArrayList();

        appIds.stream().forEach(appId -> {
            List<RoleInfo> roleInfos = roleInfoDataList.stream().filter(roleInfoData -> appId.equals(roleInfoData.getAppId())).map(roleInfoData -> {
                RoleInfo roleInfo = new RoleInfo();
                roleInfo.setId(roleInfoData.getRoleCode());
                roleInfo.setName(roleInfoData.getRoleName());
                return roleInfo;
            }).collect(Collectors.toList());
            roleInfoList.addAll(roleInfos);
        });

        roleInfoDataList.stream().filter(roleInfoData -> StringUtils.isEmpty(roleInfoData.getAppId())).forEach(roleInfoData -> {
            RoleInfo roleInfo = new RoleInfo();
            roleInfo.setId(roleInfoData.getRoleCode());
            roleInfo.setName(roleInfoData.getRoleName());
            roleInfoList.add(roleInfo);
        });
        return roleInfoList;
    }

    @Override
    public List<String> getUserOuterRoleIds(int tenantId, long outTenantId, long outUserId, String appId) {

        try {
            HeaderObj headerObj = HeaderObj.newInstance(tenantId);
            headerObj.setAppId(WebPageConstants.CROSS_APPID);

            ListUserOuterRolesByAppIdArg arg = new ListUserOuterRolesByAppIdArg();
            arg.setLinkAppId(appId);
            arg.setOuterTenantId(outTenantId);
            arg.setOuterUid(outUserId);
            arg.setTenantId(tenantId);

            RestResult<List<UserRoleInfoData>> result = appOuterRoleService.listUserOuterRolesByAppId(headerObj, arg);
            List<UserRoleInfoData> userRoleInfoDataList = result.getData();
            if (CollectionUtils.isEmpty(userRoleInfoDataList)) {
                return Lists.newArrayList();
            }
            List<String> outerIds = userRoleInfoDataList.stream().map(UserRoleInfoData::getRoleCode).collect(Collectors.toList());
            if (WebPageGraySwitch.isAllowByBusiness(appId, tenantId)) {
                outerIds.add(allOuterRoleId);
            }
            return outerIds;

        } catch (Exception e) {
            logger.error("getUserOuterRoleIds error by tenantId :{}, outTenantId :{}, outUserId :{}, appId :{}", tenantId, outTenantId, outUserId, appId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<String> getCrossAppIds(int upTenantId, String upEnterpriseAccount, long outTenantId, long outUserId) {
        List<LinkAppVo> linkAppVos = getLinkAppVo(upTenantId, upEnterpriseAccount, outTenantId, outUserId, null);
        List<String> appIds = linkAppVos.stream().map(linkAppVo -> linkAppVo.getId()).collect(Collectors.toList());
        return appIds;
    }

    @Override
    public List<LinkAppVo> getLinkAppVo(int upTenantId, String upEnterpriseAccount, long outTenantId, long outUserId, Locale locale) {
        try {
            I18N.I18NContext i18NContext = I18N.getContext();
            locale = Objects.nonNull(locale) ? locale : new Locale(i18NContext.getLanguage());

            ListEmployeeAppRolesByUpstreamEaArg arg = new ListEmployeeAppRolesByUpstreamEaArg();
            arg.setUpstreamEa(upEnterpriseAccount);
            arg.setDownstreamOuterTenantId(outTenantId);
            arg.setDownstreamOuterUid(outUserId);

            HeaderObj headerObj = HeaderObj.newInstance(upTenantId);
            headerObj.setAppId(WebPageConstants.CROSS_APPID);

            RestResult<ListEmployeeAppRolesByUpstreamEaResult> result = downstreamService.listEmployeeAppRolesByUpstreamEa(headerObj, arg);
            ListEmployeeAppRolesByUpstreamEaResult resultData = result.getData();
            if (resultData == null || CollectionUtils.isEmpty(resultData.getLinkAppVoList())) {
                return Lists.newArrayList();
            }
            List<LinkAppVo> linkAppVoList = resultData.getLinkAppVoList();
            if (uiPaasLicenseService.existMultiLanguageModule(upTenantId)) {
                Locale finalLocale = locale;
                linkAppVoList.forEach(x -> x.setName(i18nService.getCurrentI18nValue(upTenantId, getAppNametranslateKey(x.getId()),
                        x.getName(), false, finalLocale.toLanguageTag(), false)));
            }
            GetlinkAppListArg getlinkAppListArg = new GetlinkAppListArg();
            getlinkAppListArg.setTenantId(upTenantId);
            List<PaaSAppVO> paaSAppVOS = paaSAppService.getLinkAppVOList(getlinkAppListArg, locale);
            Map <String, PaaSAppVO> paaSAppVOMap = paaSAppVOS.stream().collect(Collectors.toMap(PaaSAppVO::getAppId, x -> x, (x, y) -> x));
            linkAppVoList.forEach(linkAppVo -> {
                if (paaSAppVOMap.containsKey(linkAppVo.getId())) {
                    PaaSAppVO paaSAppVO = paaSAppVOMap.get(linkAppVo.getId());
                    linkAppVo.setName(paaSAppVO.getName());
                    linkAppVo.setIcon(paaSAppVO.getIcon());
                }
            });
            return linkAppVoList;
        } catch (Exception e) {
            logger.error("getLinkAppVo error by upTenantId : {}, outTenantId : {}, outUserId : {}", upTenantId, outTenantId, outUserId, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<LinkAppData> getRelationBench(int enterpriseId, int employeeId, Locale locale) {
        try {
            HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
            headerObj.setAppId(WebPageConstants.CROSS_APPID);

            ListBenchAppsArg arg = new ListBenchAppsArg();
            arg.setTenantId(enterpriseId);
            arg.setEmployeeId(employeeId);
            RestResult<List<LinkAppData>> listRestResult = upstreamService.listBenchApps(headerObj, arg);
            List<LinkAppData> linkAppDataList = listRestResult.getData();
            if (uiPaasLicenseService.existMultiLanguageModule(enterpriseId)) {
                linkAppDataList.forEach(x -> x.setName(i18nService.getCurrentI18nValue(enterpriseId, getAppNametranslateKey(x.getId()),
                        x.getName(), false, Objects.isNull(locale) ? Locale.CHINA.toLanguageTag() : locale.toLanguageTag(), false)));
            }
            GetlinkAppListArg getlinkAppListArg = new GetlinkAppListArg();
            getlinkAppListArg.setTenantId(enterpriseId);
            List<PaaSAppVO> paaSAppVOS = paaSAppService.getLinkAppVOList(getlinkAppListArg, locale);
            Map <String, PaaSAppVO> paaSAppVOMap = paaSAppVOS.stream().collect(Collectors.toMap(PaaSAppVO::getAppId, x -> x, (x, y) -> x));
            linkAppDataList.forEach(appData -> {
                if (paaSAppVOMap.containsKey(appData.getId())) {
                    PaaSAppVO paaSAppVO = paaSAppVOMap.get(appData.getId());
                    appData.setName(paaSAppVO.getName());
                    appData.setIcon(paaSAppVO.getIcon());
                }
            });
            return listRestResult.getData();
        } catch (Exception e) {
            logger.error("listBenchApps error by enterpriseId:{}, employeeId:{}", enterpriseId, employeeId, e);
        }
        return Lists.newArrayList();
    }

    private Map<String, CrossAppVO> buildCrossAppMap(List<LinkAppVo> linkAppVoList) {

        Map<String, CrossAppVO> crossAppVOMap = Maps.newHashMap();

        linkAppVoList.stream().forEach(linkAppVo -> {
            CrossAppVO crossAppVO = new CrossAppVO();
            crossAppVO.setAppId(linkAppVo.getId());
            crossAppVO.setWebIcon(linkAppVo.getIcon());
            crossAppVO.setWebUrl(linkAppVo.getWebUrl());
            crossAppVOMap.put(linkAppVo.getId(), crossAppVO);

        });

        return crossAppVOMap;
    }

    /**
     * @param enterpriseId
     * @param linkAppIds
     * @return Map < linkAppId, LinkAppPartialData>
     */
    @Override
    public Map<String, LinkAppPartialData> getLinkAppPartialData(Integer enterpriseId, List<String> linkAppIds) {
        Map<String, LinkAppPartialData> map = new HashMap<>();
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        QueryLinkAppPartialDataArg queryLinkAppPartialDataArg = new QueryLinkAppPartialDataArg();
        queryLinkAppPartialDataArg.setLinkAppIds(linkAppIds);
        queryLinkAppPartialDataArg.setUpstreamEa(eieaConverter.enterpriseIdToAccount(enterpriseId));
        RestResult<List<LinkAppPartialData>> restResult = null;
        try {
            restResult = linkAppService.getLinkAppPartialData(headerObj, queryLinkAppPartialDataArg);
            if (null != restResult && restResult.isSuccess() && CollectionUtils.isNotEmpty(restResult.getData())) {
                restResult.getData().forEach(y -> {
                    map.put(y.getLinkAppId(), y);
                });
                return map;
            }
        } catch (Exception e) {
            log.error("getLinkAppPartialData has error  enterpriseId:{}, linkAppIds:{}", enterpriseId, linkAppIds, e);
        }
        return map;
    }

    /**
     * @param enterpriseId
     * @param employeeId
     * @return Map<upEa, Map < linkAppId, LinkAppPartialData>>
     */
    @Override
    public Map<String, Map<String, LinkAppPartialData>> listAllUpstreamOpenedLinkAppPartialDatas(Integer enterpriseId, int employeeId) {
        Map<String, Map<String, LinkAppPartialData>> map = new HashMap<>();
        HeaderObj headerObj = HeaderObj.newInstance(enterpriseId);
        ListAllUpstreamOpenedLinkAppPartialDatasArg arg = new ListAllUpstreamOpenedLinkAppPartialDatasArg();
        arg.setDownEa(eieaConverter.enterpriseIdToAccount(enterpriseId));
        arg.setDownstreamUserId(employeeId);

        RestResult<List<UpstreamOpenedLinkAppPartialData>> restResult = null;
        try {
            restResult = linkAppService.listAllUpstreamOpenedLinkAppPartialDatas(headerObj, arg);
            if (null != restResult && restResult.isSuccess() && CollectionUtils.isNotEmpty(restResult.getData())) {
                restResult.getData().forEach(x -> {
                    Map<String, LinkAppPartialData> map1 = new HashMap<>();
                    x.getAppList().forEach(y -> {
                        map1.put(y.getLinkAppId(), y);
                    });
                    map.put(x.getUpEa() + ComponentConstant.SEPARATOR_GROOT + x.getUpstreamEnterpriseName(), map1);
                });
                return map;
            }
        } catch (Exception e) {
            log.error("listAllUpstreamOpenedLinkAppPartialDatas has error  enterpriseId:{}", enterpriseId, e);
        }
        return map;
    }

}
