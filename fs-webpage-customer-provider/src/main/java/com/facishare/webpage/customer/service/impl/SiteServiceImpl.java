package com.facishare.webpage.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.*;
import com.facishare.webpage.customer.api.exception.ValidateException;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.*;
import com.facishare.webpage.customer.api.model.arg.FindSiteConfigArg;
import com.facishare.webpage.customer.api.model.arg.FindSiteListArg;
import com.facishare.webpage.customer.api.model.result.FindSiteConfigResult;
import com.facishare.webpage.customer.api.model.result.FindSiteListResult;
import com.facishare.webpage.customer.api.model.result.SiteInfoPO;
import com.facishare.webpage.customer.api.utils.RequestContextManager;
import com.facishare.webpage.customer.api.utils.UiPaasParallelUtils;
import com.facishare.webpage.customer.common.WebPageOutRoleService;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeArg;
import com.facishare.webpage.customer.common.model.QueryEmployeeScopeResult;
import com.facishare.webpage.customer.config.UIPaaSConfig;
import com.facishare.webpage.customer.config.model.DataSource;
import com.facishare.webpage.customer.constant.MenuStatus;
import com.facishare.webpage.customer.controller.model.I18nInfoDTO;
import com.facishare.webpage.customer.controller.model.arg.portal.*;
import com.facishare.webpage.customer.controller.model.result.portal.*;
import com.facishare.webpage.customer.core.util.WebPageGraySwitch;
import com.facishare.webpage.customer.dao.*;
import com.facishare.webpage.customer.dao.entity.*;
import com.facishare.webpage.customer.handler.FilterHandler;
import com.facishare.webpage.customer.model.Component;
import com.facishare.webpage.customer.model.ComponentData;
import com.facishare.webpage.customer.model.LayoutStructure;
import com.facishare.webpage.customer.processor.siteDiffProcessor.factory.SiteDiffFactory;
import com.facishare.webpage.customer.processor.siteDiffProcessor.result.DiffResults;
import com.facishare.webpage.customer.processor.siteDiffProcessor.result.EntityDiffResult;
import com.facishare.webpage.customer.service.*;
import com.facishare.webpage.customer.util.ReferenceTargetType;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.webpage.customer.api.constant.WebPageErrorCode.SOURCE_ERROR;

/**
 * Created by zhouwr on 2024/11/5.
 */
@Slf4j
@Service("siteService")
public class SiteServiceImpl implements SiteService {

    @Autowired
    private SiteEntityDao siteEntityDao;
    @Autowired
    private ThemeLayoutEntityDao themeLayoutEntityDao;
    @Autowired
    private TenantMenuDao tenantMenuDao;
    @Autowired
    private HomePageLayoutDao homePageLayoutDao;
    @Autowired
    private SiteDraftService siteDraftService;
    @Autowired
    private SiteDraftEntityDao siteDraftEntityDao;
    @Autowired
    private PaaSAppDao paaSAppDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private WebPageOutRoleService webPageOutRoleService;
    @Autowired
    private CrmPassCacheManager crmPassCacheManager;
    @Autowired
    private LinkAppObjectAssociationDao linkAppObjectAssociationDao;
    @Resource
    private ReferenceEntityDao referenceEntityDao;
    @Resource
    private CmsResourceService cmsResourceService;
    @Resource
    private WorkSpaceEntityDao workSpaceEntityDao;
    @Autowired
    private ThemeStyleEntityDao themeStyleEntityDao;
    @Autowired
    private SiteConfigEntityDao siteConfigEntityDao;
    @Autowired
    private SiteI18nQueryService siteI18nQueryService;
    @Autowired
    private SiteDiffFactory siteDiffFactory;
    @Resource
    private UIPaaSConfig uiPaaSConfig;

    public static final String DRAFT_SITE_API_NAME = "__draft";

    @Override
    public SiteDraftEntity publishSiteDraft(User user, PublishSiteDraftArg arg) {
        arg.validateAndFix();
        SiteDraftEntity siteDraftEntity = siteDraftEntityDao.findDraftBySiteApiName(user.getTenantId(), arg.getSiteApiName(), arg.getClientType());
        if (Objects.isNull(siteDraftEntity)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        SiteEntity siteEntity = siteEntityDao.findByApiName(user.getTenantId(), arg.getSiteApiName());
        if (Objects.isNull(siteEntity)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }

        JSONObject draftData = JSONObject.parseObject(siteDraftEntity.getDraftData());
        Object themeLayoutList = Optional.ofNullable(draftData.get("themeLayoutList")).orElse(Lists.newArrayList());
        List<ThemeLayoutDTO> themeLayoutDTOList = JSON.parseArray(JSON.toJSONString(themeLayoutList), ThemeLayoutDTO.class);
        Object menuList = Optional.ofNullable(draftData.get("menuList")).orElse(Lists.newArrayList());
        List<SiteMenuDTO> siteMenuDTOList = JSON.parseArray(JSON.toJSONString(menuList), SiteMenuDTO.class);
        Object pageList = Optional.ofNullable(draftData.get("pageList")).orElse(Lists.newArrayList());
        List<SitePageDTO> sitePageDTOList = JSON.parseArray(JSON.toJSONString(pageList), SitePageDTO.class);
        Object themeStyleList = Optional.ofNullable(draftData.get("themeStyleList")).orElse(Lists.newArrayList());
        List<ThemeStyleDTO> themeStyleDTOList = JSON.parseArray(JSON.toJSONString(themeStyleList), ThemeStyleDTO.class);
        Object i18nInfoList = Optional.ofNullable(draftData.get("i18nInfoList")).orElse(Lists.newArrayList());
        List<I18nInfoDTO> i18nInfoDTOList = JSON.parseArray(JSON.toJSONString(i18nInfoList), I18nInfoDTO.class);
        Object langList = Optional.ofNullable(draftData.get("langList")).orElse(Lists.newArrayList());
        List<SiteLang> siteLangList = JSON.parseArray(JSON.toJSONString(langList), SiteLang.class);


        Object fileInfoList = Optional.ofNullable(draftData.get("fileInfoList")).orElse(Lists.newArrayList());
        List<FileInfoDTO> fileInfoVOList = JSON.parseArray(JSON.toJSONString(fileInfoList), FileInfoDTO.class);

        Object siteConfig = draftData.get("siteConfig");
        SiteConfigDTO siteConfigDTO = Objects.isNull(siteConfig) ? null : JSON.parseObject(JSON.toJSONString(siteConfig), SiteConfigDTO.class);
        SiteConfigEntity siteConfigEntity = Objects.isNull(siteConfigDTO) ? null : siteConfigDTO.toEntity(siteEntity, arg.getClientType());

        List<ThemeLayoutEntity> themeLayoutEntityList = themeLayoutDTOList.stream()
                .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                .collect(Collectors.toList());
        List<TenantMenuEntity> tenantMenuEntityList = siteMenuDTOList.stream()
                .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                .collect(Collectors.toList());
        List<HomePageLayoutEntity> pageLayoutEntityList = sitePageDTOList.stream()
                .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                .collect(Collectors.toList());
        List<ThemeStyleEntity> themeStyleEntityList = themeStyleDTOList.stream()
                .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                .collect(Collectors.toList());
        List<ReferenceEntity> fileReferenceEntityList = fileInfoVOList.stream()
                .map(x -> x.toEntity(user, siteEntity.getApiName()))
                .collect(Collectors.toList());
        List<ReferenceEntity> i18nReferenceEntityList = i18nInfoDTOList.stream().map(x -> x.toEntity(siteEntity))
                .collect(Collectors.toList());


        publishSitePages(user, siteEntity, siteDraftEntity.getVersion(), themeLayoutEntityList, tenantMenuEntityList,
                pageLayoutEntityList, themeStyleEntityList, siteConfigEntity, fileReferenceEntityList, i18nReferenceEntityList,
                i18nInfoDTOList, siteLangList, arg.getClientType());
        return siteDraftEntity;
    }


    @Override
    public void createSiteInfo(User user, SiteInfoDTO siteInfo) {
        siteInfo.valid();
        SiteEntity siteEntity = siteInfo.toEntity();
        //apiName查重
        SiteEntity oldSiteEntity = siteEntityDao.findByApiNameIncludeDisable(user.getTenantId(), siteEntity.getApiName());
        if (Objects.nonNull(oldSiteEntity)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_API_NAME_DUPLICATE);
        }
        //siteId查重
        oldSiteEntity = siteEntityDao.findBySiteIdIncludeDisable(user.getTenantId(), siteEntity.getSiteId());
        if (Objects.nonNull(oldSiteEntity)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_ID_DUPLICATE);
        }
        siteEntityDao.save(user, siteEntity);
    }

    @Override
    public SiteInfoVO findSiteInfo(User user, String siteApiName) {
        SiteEntity siteEntity = siteEntityDao.findByApiNameIncludeDisable(user.getTenantId(), siteApiName);
        if (Objects.isNull(siteEntity)) {
            return null;
        }
        return SiteInfoVO.of(siteEntity);
    }

    @Override
    public void updateSiteInfo(User user, SiteInfoDTO siteInfo) {
        if (StringUtils.isBlank(siteInfo.getId()) && StringUtils.isBlank(siteInfo.getApiName())) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        SiteEntity siteEntity = siteInfo.toEntity();
        siteEntityDao.update(user, siteEntity);
    }

    private String getDraftSiteApiName(String apiName) {
        return apiName + DRAFT_SITE_API_NAME;
    }

    @Override
    public SiteDraftEntity updateSitePages(User user, UpdateSitePagesArg arg) {
        arg.validAndFix(String.valueOf(user.getTenantId()));
        //查询站点信息
        SiteEntity siteEntity = siteEntityDao.findByApiNameIncludeDisable(user.getTenantId(), arg.getSiteApiName());
        if (Objects.isNull(siteEntity)) {
            log.warn("site not found,tenantId:{},siteApiName:{}", user.getTenantId(), arg.getSiteApiName());
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }


        SiteDraftInfoDTO siteDraftInfoDTO = new SiteDraftInfoDTO();
        siteDraftInfoDTO.setAppId(siteEntity.getAppId());
        siteDraftInfoDTO.setSiteApiName(siteEntity.getApiName());
        siteDraftInfoDTO.setDescription(siteEntity.getDescription());
        JSONObject draftData = new JSONObject();
        draftData.put("themeLayoutList", arg.getThemeLayoutList());
        draftData.put("menuList", arg.getMenuList());
        draftData.put("pageList", arg.getPageList());
        draftData.put("themeStyleList", arg.getThemeStyleList());
        draftData.put("siteConfig", arg.getSiteConfig());
        draftData.put("fileInfoList", arg.getFileInfoList());
        draftData.put("i18nInfoList", arg.getI18nInfoList());
        draftData.put("langList", arg.getLangList());
        siteDraftInfoDTO.setDraftData(draftData);
        siteDraftInfoDTO.setClientType(arg.getClientType());
        SiteDraftEntity siteDraftEntity = siteDraftService.creatOrUpdateSiteDraftInfo(user, siteDraftInfoDTO);

        if (CollectionUtils.isNotEmpty(arg.getFileInfoList()) && Boolean.FALSE.equals(arg.getIsPublish())) {
            List<ReferenceEntity> fileReferenceEntityList = arg.getFileInfoList().stream()
                    .map(x -> x.toEntity(user, getDraftSiteApiName(siteEntity.getApiName())))
                    .collect(Collectors.toList());

            EntityDiffResult<ReferenceEntity> fileReferenceDiffResult = siteDiffFactory.getReferenceProcessor()
                    .processDiff(user, siteEntity, fileReferenceEntityList, arg.getClientType(),
                            ReferenceTargetType.FILE.getCode(), arg.getIsPublish());

            referenceEntityDao.batchDelete(fileReferenceDiffResult.getToDelete());
            referenceEntityDao.batchSave(fileReferenceDiffResult.getToUpdate());
            referenceEntityDao.batchSave(fileReferenceDiffResult.getToCreate());
        }


        if (Boolean.TRUE.equals(arg.getIsPublish())) {
            validateFileInfo(user, arg.getFileInfoList());

            List<ThemeLayoutEntity> newThemeLayoutEntityList = CollectionUtils.emptyIfNull(arg.getThemeLayoutList()).stream()
                    .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                    .collect(Collectors.toList());
            List<TenantMenuEntity> newMenuEntityList = CollectionUtils.emptyIfNull(arg.getMenuList()).stream()
                    .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                    .collect(Collectors.toList());
            List<HomePageLayoutEntity> newPageEntityList = CollectionUtils.emptyIfNull(arg.getPageList()).stream()
                    .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                    .collect(Collectors.toList());
            List<ThemeStyleEntity> newThemeStyleEntityList = CollectionUtils.emptyIfNull(arg.getThemeStyleList()).stream()
                    .map(x -> x.toEntity(siteEntity, arg.getClientType()))
                    .collect(Collectors.toList());
            List<ReferenceEntity> fileReferenceEntityList = CollectionUtils.emptyIfNull(arg.getFileInfoList()).stream()
                    .map(x -> x.toEntity(user, siteEntity.getApiName()))
                    .collect(Collectors.toList());

            SiteConfigEntity siteConfigEntity = Objects.isNull(arg.getSiteConfig()) ? null : arg.getSiteConfig().toEntity(siteEntity, arg.getClientType());

            // 将多语信息转换为引用关系实体
            List<ReferenceEntity> i18nReferenceEntityList = convertI18nInfoToReferenceEntities(
                    siteEntity, arg.getI18nInfoList(), arg.getClientType());

            publishSitePages(user, siteEntity, siteDraftEntity.getVersion(), newThemeLayoutEntityList, newMenuEntityList,
                    newPageEntityList, newThemeStyleEntityList, siteConfigEntity, fileReferenceEntityList,
                    i18nReferenceEntityList, arg.getI18nInfoList(), arg.getLangList(), arg.getClientType());
        } else if (siteEntity.published(arg.getClientType())) {
            siteEntityDao.modifySiteAfterPublish(user, siteEntity.getApiName(), arg.getClientType());
        }
        return siteDraftEntity;
    }

    private void validateFileInfo(User user, List<FileInfoDTO> fileInfoList) {
        if (CollectionUtils.isEmpty(fileInfoList)) {
            return;
        }
        List<String> fileApiNameList = fileInfoList.stream().map(FileInfoDTO::getApiName).collect(Collectors.toList());
        List<FileEntity> fileEntityList = cmsResourceService.findByApiNamesIncludesDelete(user, fileApiNameList);
        Map<String, FileEntity> fileEntityMap = fileEntityList.stream()
                .collect(Collectors.toMap(FileEntity::getApiName, it -> it, (x1, x2) -> x1));
        List<String> failFileList = Lists.newArrayList();
        for (FileInfoDTO fileInfoDTO : fileInfoList) {
            String apiName = fileInfoDTO.getApiName();
            FileEntity fileEntity = fileEntityMap.get(apiName);
            //库里都没查到的数据，不处理，在下发的时候也是一个空的url（这种情况认为是前端给的脏数据，正常是不存在的）
            if (Objects.isNull(fileEntity)) {
                log.warn("file not found,apiName:{},workSpaceApiName:{}", apiName, fileInfoDTO.getWorkSpaceApiName());
                continue;
            }
            if (CmsStatus.DELETE.equals(fileEntity.getStatus())) {
                failFileList.add(fileEntity.getName());
            }
        }
        if (CollectionUtils.isNotEmpty(failFileList)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.FILE_STATUS_NOT_ENABLE, failFileList.toString());
        }
        Set<String> workSpaceApiNameList = fileEntityList.stream().map(FileEntity::getWorkSpace).collect(Collectors.toSet());
        List<WorkSpaceEntity> workSpaceList = workSpaceEntityDao.findWorkSpaceIncludesDeleted(user.getTenantId(), workSpaceApiNameList);

        List<String> failWorkSpace = workSpaceList.stream()
                .filter(x -> CmsStatus.DELETE.equals(x.getStatus()))
                .map(WorkSpaceEntity::getName)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(failWorkSpace)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.WORK_SPACE_STATUS_NOT_ENABLE, failWorkSpace.toString());
        }
    }


    private void publishSitePages(User user,
                                  SiteEntity siteEntity,
                                  Long version,
                                  List<ThemeLayoutEntity> newThemeLayoutEntityList,
                                  List<TenantMenuEntity> newMenuEntityList,
                                  List<HomePageLayoutEntity> newPageEntityList,
                                  List<ThemeStyleEntity> newThemeStyleEntityList,
                                  SiteConfigEntity siteConfigEntity,
                                  List<ReferenceEntity> fileReferenceEntityList,
                                  List<ReferenceEntity> i18nReferenceEntityList,
                                  List<I18nInfoDTO> i18nInfoList, List<SiteLang> langList,
                                  String clientType) {
        SlowLog slowLog = GlobalStopWatch.create("publishSitePages", 100L);


        DiffResults diffResults = diffSiteInnerData(user, siteEntity, newMenuEntityList, newPageEntityList,
                newThemeStyleEntityList, newThemeLayoutEntityList, fileReferenceEntityList, i18nReferenceEntityList, clientType);
        slowLog.lap("diffResults");

        EntityDiffResult menuDiff = diffResults.getMenuDiff();
        EntityDiffResult pageDiff = diffResults.getPageDiff();
        EntityDiffResult themeStyleDiff = diffResults.getThemeStyleDiff();
        EntityDiffResult themeLayoutDiff = diffResults.getThemeLayoutDiff();
        EntityDiffResult fileDiff = diffResults.getFileDiff();
        EntityDiffResult i18nDiff = diffResults.getI18nDiff();

        // 同步多语信息到翻译工作台（只同步新增和更新的）
        syncI18nInfoToTranslationWorkbench(user, siteEntity, i18nInfoList, i18nDiff.getToCreate(), i18nDiff.getToUpdate());
        slowLog.lap("syncI18nInfoToTranslationWorkbench");


        themeStyleEntityDao.updateStatus(user, themeStyleDiff.getToDelete(), PaaSStatus.delete);
        themeStyleEntityDao.batchUpdate(user, themeStyleDiff.getToUpdate());
        themeStyleEntityDao.batchSave(user, themeStyleDiff.getToCreate());
        slowLog.lap("update themeStyleEntity");


        themeLayoutEntityDao.updateStatus(user, themeLayoutDiff.getToDelete(), PaaSStatus.delete);
        themeLayoutEntityDao.batchUpdate(user, themeLayoutDiff.getToUpdate());
        themeLayoutEntityDao.batchSave(user, themeLayoutDiff.getToCreate());
        slowLog.lap("update themeLayoutEntity");


        tenantMenuDao.updateStatus(user, menuDiff.getToDelete(), MenuStatus.deleteStatus);
        tenantMenuDao.batchUpdate(user, menuDiff.getToUpdate());
        tenantMenuDao.batchSave(user, menuDiff.getToCreate());
        slowLog.lap("update tenantMenu");


        homePageLayoutDao.deleteByIds(user, pageDiff.getToDelete());
        homePageLayoutDao.batchUpdate(user, pageDiff.getToUpdate());
        homePageLayoutDao.batchSave(user, pageDiff.getToCreate());
        slowLog.lap("update  homePageLayout");


        List<String> referenceDelete = Lists.newArrayList();
        slowLog.lap("update findDraftReferenceEntityList");
        referenceDelete.addAll(i18nDiff.getToDelete());
        referenceDelete.addAll(fileDiff.getToDelete());

        List<ReferenceEntity> referenceUpdate = Lists.newArrayList();
        referenceUpdate.addAll(fileDiff.getToUpdate());
        referenceUpdate.addAll(i18nDiff.getToUpdate());

        List<ReferenceEntity> referenceCreate = Lists.newArrayList();
        referenceCreate.addAll(fileDiff.getToCreate());
        referenceCreate.addAll(i18nDiff.getToCreate());

        referenceEntityDao.batchDelete(referenceDelete);
        referenceEntityDao.batchUpdate(user, referenceUpdate);
        referenceEntityDao.batchSave(referenceCreate);
        slowLog.lap("update  referenceEntity");


        if (Objects.nonNull(siteConfigEntity)) {
            siteConfigEntityDao.save(user, siteConfigEntity);
            slowLog.lap("save siteConfigEntity");
        }

        if (CollectionUtils.isNotEmpty(langList)) {
            siteEntity.setLangList(langList);
            siteEntityDao.update(user, siteEntity);
            slowLog.lap("save siteEntity");
        }

        siteEntityDao.publishSite(user, siteEntity.getApiName(), Optional.ofNullable(version).orElse(0L), clientType);
        slowLog.lap("publishSite");

        slowLog.stop();

    }

    private DiffResults diffSiteInnerData(User user, SiteEntity siteEntity, List<TenantMenuEntity> newMenuEntityList,
                                          List<HomePageLayoutEntity> newPageEntityList, List<ThemeStyleEntity> newThemeStyleEntityList,
                                          List<ThemeLayoutEntity> newThemeLayoutEntityList, List<ReferenceEntity> fileReferenceEntityList,
                                          List<ReferenceEntity> i18nReferenceEntityList, String clientType) {
        SlowLog slowLog = GlobalStopWatch.create("diffSiteData", 100L);

        if (WebPageGraySwitch.isAllowForEi(WebPageGraySwitch.SIET_DIFF_PARALLEL_DIFF, user.getTenantId())) {
            return parallelGetDiffResults(user, siteEntity, newMenuEntityList, newPageEntityList, newThemeStyleEntityList, newThemeLayoutEntityList, fileReferenceEntityList, i18nReferenceEntityList, clientType);
        } else {
            EntityDiffResult<TenantMenuEntity> menuDiffResult = siteDiffFactory.getMenuProcessor()
                    .processDiff(user, siteEntity, newMenuEntityList, clientType, null, true);
            slowLog.lap("diff TenantMenuEntity");

            EntityDiffResult<ThemeStyleEntity> themeStyleDiffResult = siteDiffFactory.getThemeStyleProcessor()
                    .processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null, true);
            slowLog.lap("diff ThemeStyleEntity");


            EntityDiffResult<HomePageLayoutEntity> pageDiffResult = siteDiffFactory.getPageProcessor()
                    .processDiff(user, siteEntity, newPageEntityList, clientType, null, true);
            slowLog.lap("diff HomePageLayoutEntity");


            EntityDiffResult<ThemeLayoutEntity> themeLayoutDiffResult = siteDiffFactory.getThemeLayoutProcessor()
                    .processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, true);
            slowLog.lap("diff ThemeLayoutEntity");


            EntityDiffResult<ReferenceEntity> fileDiffResult = siteDiffFactory.getReferenceProcessor()
                    .processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.getCode(), true);
            slowLog.lap("diff fileInfoDiff");


            EntityDiffResult<ReferenceEntity> i18nDiffResult = siteDiffFactory.getReferenceProcessor()
                    .processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.getCode(), true);
            slowLog.lap("diff i18nInfoDiff");

            slowLog.stop();

            return new DiffResults(themeStyleDiffResult, themeLayoutDiffResult, menuDiffResult, pageDiffResult,
                    fileDiffResult, i18nDiffResult);
        }

    }

    @NotNull
    private DiffResults parallelGetDiffResults(User user, SiteEntity siteEntity, List<TenantMenuEntity> newMenuEntityList, List<HomePageLayoutEntity> newPageEntityList, List<ThemeStyleEntity> newThemeStyleEntityList, List<ThemeLayoutEntity> newThemeLayoutEntityList, List<ReferenceEntity> fileReferenceEntityList, List<ReferenceEntity> i18nReferenceEntityList, String clientType) {
        ParallelUtils.ParallelTask parallelTask = UiPaasParallelUtils.createParallelTask();

        final AtomicReference<EntityDiffResult> menuDiffRef = new AtomicReference<>();
        final AtomicReference<EntityDiffResult> pageDiffRef = new AtomicReference<>();
        final AtomicReference<EntityDiffResult> themeStyleDiffRef = new AtomicReference<>();
        final AtomicReference<EntityDiffResult> themeLayoutDiffRef = new AtomicReference<>();
        final AtomicReference<EntityDiffResult> i18nRefDiffResult = new AtomicReference<>();
        final AtomicReference<EntityDiffResult> fileRefDiffResult = new AtomicReference<>();
        final List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());

        parallelTask.submit(() -> {
            try {
                EntityDiffResult<TenantMenuEntity> menuDiffResult = siteDiffFactory.getMenuProcessor()
                        .processDiff(user, siteEntity, newMenuEntityList, clientType, null, true);
                menuDiffRef.set(menuDiffResult);
            } catch (Exception e) {
                exceptions.add(e);
            }
        });

        parallelTask.submit(() -> {
            try {
                EntityDiffResult<HomePageLayoutEntity> pageDiffResult = siteDiffFactory.getPageProcessor()
                        .processDiff(user, siteEntity, newPageEntityList, clientType, null, true);
                pageDiffRef.set(pageDiffResult);
            } catch (Exception e) {
                exceptions.add(e);
            }
        });

        parallelTask.submit(() -> {
            try {
                EntityDiffResult<ThemeStyleEntity> themeStyleDiffResult = siteDiffFactory.getThemeStyleProcessor()
                        .processDiff(user, siteEntity, newThemeStyleEntityList, clientType, null, true);
                themeStyleDiffRef.set(themeStyleDiffResult);
            } catch (Exception e) {
                exceptions.add(e);
            }
        });
        parallelTask.submit(() -> {
            try {
                EntityDiffResult<ThemeLayoutEntity> themeLayoutDiffResult = siteDiffFactory.getThemeLayoutProcessor()
                        .processDiff(user, siteEntity, newThemeLayoutEntityList, clientType, null, true);
                themeLayoutDiffRef.set(themeLayoutDiffResult);
            } catch (Exception e) {
                exceptions.add(e);
            }
        });
        parallelTask.submit(() -> {
            try {
                EntityDiffResult<ReferenceEntity> fileDiffResult = siteDiffFactory.getReferenceProcessor()
                        .processDiff(user, siteEntity, fileReferenceEntityList, clientType, ReferenceTargetType.FILE.getCode(), true);
                fileRefDiffResult.set(fileDiffResult);
            } catch (Exception e) {
                exceptions.add(e);
            }
        });
        parallelTask.submit(() -> {
            try {
                EntityDiffResult<ReferenceEntity> i18nDiffResult = siteDiffFactory.getReferenceProcessor()
                        .processDiff(user, siteEntity, i18nReferenceEntityList, clientType, ReferenceTargetType.I18N.getCode(), true);
                i18nRefDiffResult.set(i18nDiffResult);
            } catch (Exception e) {
                exceptions.add(e);
            }
        });

        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            throw new RuntimeException(e);
        }
        return new DiffResults(themeStyleDiffRef.get(), themeLayoutDiffRef.get(), menuDiffRef.get(), pageDiffRef.get(),
                fileRefDiffResult.get(), i18nRefDiffResult.get());
    }

    public List<String> findDraftReferenceEntityList(User user, SiteEntity siteEntity) {
        ReferenceEntity referenceCondition = new ReferenceEntity();
        referenceCondition.setTenantId(user.getTenantId());
        referenceCondition.setIndexName("siteApiName");
        referenceCondition.setIndexValue(getDraftSiteApiName(siteEntity.getApiName()));
        List<ReferenceEntity> referenceByCondition = referenceEntityDao.findReferenceByCondition(referenceCondition);
        return referenceByCondition.stream().map(ReferenceEntity::getId).collect(Collectors.toList());
    }


    @Override
    public void enableSite(User user, String siteApiName) {
        siteEntityDao.updateStatusByApiName(user, siteApiName, PaaSStatus.enable);
    }

    @Override
    public void disableSite(User user, String siteApiName) {
        siteEntityDao.updateStatusByApiName(user, siteApiName, PaaSStatus.disable);
    }

    @Override
    public void deleteSite(User user, String siteApiName) {
        siteEntityDao.updateStatusByApiName(user, siteApiName, PaaSStatus.delete);
    }

    @Override
    public List<SiteInfoVO> findSitesByAppId(int tenantId, String appId) {
        List<SiteEntity> siteEntityList = siteEntityDao.findByAppIdIncludeDisable(tenantId, appId);
        return siteEntityList.stream().map(SiteInfoVO::of).collect(Collectors.toList());
    }

    @Override
    public FindSiteResult findSiteByApiNameForManager(String siteApiName, String clientType) {
        User user = RequestContextManager.getUser();
        int tenantId = user.getTenantId();
        SiteEntity siteEntity = siteEntityDao.findByApiNameIncludeDisable(tenantId, siteApiName);
        if (Objects.isNull(siteEntity)) {
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_NOT_FOUND_ERROR, SOURCE_ERROR);
        }

        SiteDraftEntity siteDraftEntity = siteDraftEntityDao.findDraftBySiteApiName(tenantId, siteApiName, clientType);
        if (Objects.nonNull(siteDraftEntity)) {
            JSONObject draftData = JSONObject.parseObject(siteDraftEntity.getDraftData());
            Object themeLayoutList = Optional.ofNullable(draftData.get("themeLayoutList")).orElse(Lists.newArrayList());
            List<ThemeLayoutVO> themeLayoutVOList = JSON.parseArray(JSON.toJSONString(themeLayoutList), ThemeLayoutVO.class)
                    .stream().collect(Collectors.toList());
            Object menuList = Optional.ofNullable(draftData.get("menuList")).orElse(Lists.newArrayList());
            List<SiteMenuVO> siteMenuVOList = JSON.parseArray(JSON.toJSONString(menuList), SiteMenuVO.class)
                    .stream().collect(Collectors.toList());
            Object pageList = Optional.ofNullable(draftData.get("pageList")).orElse(Lists.newArrayList());
            List<SitePageVO> sitePageVOList = JSON.parseArray(JSON.toJSONString(pageList), SitePageVO.class)
                    .stream().collect(Collectors.toList());
            Object siteConfig = draftData.get("siteConfig");
            SiteConfigVO siteConfigVO = Objects.isNull(siteConfig) ? null : JSON.parseObject(JSON.toJSONString(siteConfig), SiteConfigVO.class);
            Object themeStyleList = Optional.ofNullable(draftData.get("themeStyleList")).orElse(Lists.newArrayList());
            List<ThemeStyleVO> themeStyleVOList = JSON.parseArray(JSON.toJSONString(themeStyleList), ThemeStyleVO.class)
                    .stream().collect(Collectors.toList());
            Object fileInfoList = Optional.ofNullable(draftData.get("fileInfoList")).orElse(Lists.newArrayList());
            List<FileInfoVO> fileInfoVOList = JSON.parseArray(JSON.toJSONString(fileInfoList), FileInfoVO.class)
                    .stream().collect(Collectors.toList());
            List<FileEntity> fileEntityList = cmsResourceService.findFileEntityByApiNames(user, fileInfoVOList.stream().map(FileInfoVO::getApiName).collect(Collectors.toList()));
            //批量计算url
            Map<String, String> fileUrl = cmsResourceService.getFileUrl(user, fileEntityList);
            fileInfoVOList.forEach(x -> x.setUrl(Objects.isNull(fileUrl.get(x.getApiName())) ? "" : fileUrl.get(x.getApiName())));

            // 查询多语信息（从草稿中获取或从发布版本中获取）
            List<I18nInfoDTO> i18nInfoList = queryI18nInfoForSite(tenantId, siteApiName, siteEntity.getLangList(), draftData);

            Object langList = Optional.ofNullable(draftData.get("langList")).orElse(Lists.newArrayList());
            List<SiteLang> siteLangs = JSON.parseArray(JSON.toJSONString(langList), SiteLang.class).
                    stream().collect(Collectors.toList());

            return FindSiteResult.builder()
                    .siteInfo(SiteInfoVO.of(siteEntity))
                    .themeLayoutList(themeLayoutVOList)
                    .menuList(siteMenuVOList)
                    .pageList(sitePageVOList)
                    .fileInfoList(fileInfoVOList)
                    .siteConfig(siteConfigVO)
                    .langList(siteLangs)
                    .themeStyleList(themeStyleVOList)
                    .i18nInfoList(i18nInfoList)
                    .build();
        }

        //没有草稿的话，从正式版本中获取
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageLayoutDao.findBySiteApiName(tenantId,
                siteEntity.getAppId(), siteEntity.getApiName(), clientType);
        List<SitePageVO> sitePageVOList = homePageLayoutEntityList.stream().map(SitePageVO::of).collect(Collectors.toList());
        List<ThemeLayoutEntity> themeLayoutEntityList = themeLayoutEntityDao.findBySiteApiNameIncludeDisable(tenantId,
                siteEntity.getApiName(), clientType);
        List<ThemeLayoutVO> themeLayoutVOList = themeLayoutEntityList.stream().map(ThemeLayoutVO::of).collect(Collectors.toList());
        List<TenantMenuEntity> tenantMenuEntityList = tenantMenuDao.findBySiteApiNameIncludeDisable(tenantId,
                siteEntity.getApiName(), clientType);
        List<SiteMenuVO> siteMenuVOList = tenantMenuEntityList.stream().map(SiteMenuVO::of).collect(Collectors.toList());
        List<ThemeStyleEntity> themeStyleEntityList = themeStyleEntityDao.findBySiteApiNameIncludeDisable(tenantId,
                siteEntity.getApiName(), clientType);
        List<ThemeStyleVO> themeStyleVOList = themeStyleEntityList.stream().map(ThemeStyleVO::of).collect(Collectors.toList());
        SiteConfigEntity siteConfigEntity = siteConfigEntityDao.findBySiteApiName(tenantId,
                siteEntity.getApiName(), clientType);
        SiteConfigVO siteConfigVO = Objects.isNull(siteConfigEntity) ? null : SiteConfigVO.of(siteConfigEntity);
        List<FileInfoVO> fileInfoVOList = getFileInfoListBySiteApiName(user, siteEntity.getApiName());

        // 查询多语信息（从发布版本中获取）
        List<I18nInfoDTO> i18nInfoList = queryI18nInfoForSite(tenantId, siteApiName, siteEntity.getLangList(), null);

        return FindSiteResult.builder()
                .siteInfo(SiteInfoVO.of(siteEntity))
                .themeLayoutList(themeLayoutVOList)
                .menuList(siteMenuVOList)
                .pageList(sitePageVOList)
                .fileInfoList(fileInfoVOList)
                .langList(siteEntity.getLangList())
                .siteConfig(siteConfigVO)
                .themeStyleList(themeStyleVOList)
                .i18nInfoList(i18nInfoList)
                .build();
    }

    /**
     * 查询站点的多语信息
     *
     * @param tenantId    租户ID
     * @param siteApiName 站点ApiName
     * @param langList    站点语言配置列表
     * @param draftData   草稿数据（可为null，表示从发布版本查询）
     * @return 多语信息列表
     */
    private List<I18nInfoDTO> queryI18nInfoForSite(Integer tenantId, String siteApiName,
                                                   List<SiteLang> langList, JSONObject draftData) {
        try {
            // 转换SiteLang为SiteLangDTO
            List<SiteLangDTO> siteLangDTOList = convertSiteLangToDTO(langList);

            // 如果有草稿数据，优先从草稿中获取多语信息
            if (draftData != null) {
                Object i18nInfoListObj = draftData.get("i18nInfoList");
                if (i18nInfoListObj != null) {
                    List<I18nInfoDTO> draftI18nInfoList = JSON.parseArray(JSON.toJSONString(i18nInfoListObj), I18nInfoDTO.class);
                    if (CollectionUtils.isNotEmpty(draftI18nInfoList)) {
                        log.debug("Found i18n info in draft for site: {}, count: {}", siteApiName, draftI18nInfoList.size());
                        return draftI18nInfoList;
                    }
                }
                return Lists.newArrayList();
            }

            // 从发布版本中查询多语信息
            List<I18nInfoDTO> i18nInfoList = siteI18nQueryService.queryI18nInfoForSite(tenantId, siteApiName, siteLangDTOList);
            log.debug("Queried i18n info from published version for site: {}, count: {}", siteApiName, i18nInfoList.size());

            return i18nInfoList;

        } catch (Exception e) {
            log.error("Failed to query i18n info for site: {}", siteApiName, e);
            // 不抛出异常，返回空列表，保证主流程不受影响
            return Collections.emptyList();
        }
    }

    /**
     * 转换SiteLang列表为SiteLangDTO列表
     *
     * @param langList SiteLang列表
     * @return SiteLangDTO列表
     */
    private List<SiteLangDTO> convertSiteLangToDTO(List<SiteLang> langList) {
        if (CollectionUtils.isEmpty(langList)) {
            return Collections.emptyList();
        }

        return langList.stream()
                .map(SiteLang::toDTO)
                .collect(Collectors.toList());
    }


    /**
     * 从站点语言配置中获取默认语言
     *
     * @param siteEntity 站点实体
     * @return 默认语言代码
     */
    private String getDefaultLanguageFromSite(SiteEntity siteEntity) {
        if (CollectionUtils.isEmpty(siteEntity.getLangList())) {
            return "zh_CN"; // 默认返回中文
        }

        return siteEntity.getLangList().stream()
                .filter(SiteLang::isDefaultLanguage)
                .map(SiteLang::getLang)
                .findFirst()
                .orElse("zh_CN");
    }

    /**
     * 查询站点的多语信息（用户态专用）
     * 只返回站点langList中配置的语言，兼容老站点
     *
     * @param tenantId    租户ID
     * @param siteApiName 站点ApiName
     * @param langList    站点语言配置列表
     * @return 多语信息列表
     */
    private List<I18nInfoDTO> queryI18nInfoForSiteView(Integer tenantId, String siteApiName, List<SiteLang> langList) {
        try {
            // 转换SiteLang为SiteLangDTO
            List<SiteLangDTO> siteLangDTOList = convertSiteLangToDTO(langList);

            // 检查站点是否支持多语功能
            if (!siteI18nQueryService.isSiteMultilingualEnabled(siteLangDTOList)) {
                log.debug("Site {} does not support multilingual features for view, returning empty list", siteApiName);
                return Collections.emptyList();
            }

            // 从发布版本中查询多语信息（用户态不查询草稿）
            List<I18nInfoDTO> i18nInfoList = siteI18nQueryService.queryI18nInfoForSite(tenantId, siteApiName, siteLangDTOList);
            log.debug("Queried i18n info for site view: {}, count: {}", siteApiName, i18nInfoList.size());

            return i18nInfoList;

        } catch (Exception e) {
            log.error("Failed to query i18n info for site view: {}", siteApiName, e);
            // 不抛出异常，返回空列表，保证主流程不受影响
            return Collections.emptyList();
        }
    }

    /**
     * 将多语信息转换为引用关系实体
     *
     * @param siteEntity   站点实体
     * @param i18nInfoList 多语信息列表
     * @param clientType   客户端类型
     * @return 引用关系实体列表
     */
    private List<ReferenceEntity> convertI18nInfoToReferenceEntities(SiteEntity siteEntity,
                                                                     List<I18nInfoDTO> i18nInfoList, String clientType) {
        if (CollectionUtils.isEmpty(i18nInfoList)) {
            return new ArrayList<>();
        }

        return i18nInfoList.stream()
                .map(i18nInfo -> i18nInfo.toEntity(siteEntity))
                .collect(Collectors.toList());
    }

    /**
     * 根据引用关系过滤多语信息
     *
     * @param i18nInfoList        多语信息列表
     * @param referenceEntityList 引用关系列表
     * @return 过滤后的多语信息列表
     */
    private List<I18nInfoDTO> filterI18nInfoByReferences(List<I18nInfoDTO> i18nInfoList,
                                                         List<ReferenceEntity> referenceEntityList) {
        if (CollectionUtils.isEmpty(i18nInfoList) || CollectionUtils.isEmpty(referenceEntityList)) {
            return new ArrayList<>();
        }

        Set<String> referenceKeys = referenceEntityList.stream()
                .map(ref -> ref.getTargetId() + "_" + ref.getSourceId())
                .collect(Collectors.toSet());

        return i18nInfoList.stream()
                .filter(i18nInfo -> {
                    String key = i18nInfo.getKey() + "_" + i18nInfo.getSourceId();
                    return referenceKeys.contains(key);
                })
                .collect(Collectors.toList());
    }

    /**
     * 同步多语信息到翻译工作台
     *
     * @param i18nInfoList 多语信息列表
     */
    private void syncI18nInfoToTranslationWorkbench(User user, SiteEntity siteEntity, List<I18nInfoDTO> i18nInfoList,
                                                    List<ReferenceEntity> i18nReferenceEntityList2Create,
                                                    List<ReferenceEntity> i18nReferenceEntityList2Update) {
        if (CollectionUtils.isEmpty(i18nInfoList)) {
            log.warn("No i18n info to sync for site: {}", siteEntity.getApiName());
            return;
        }
        try {
            // 获取需要同步的多语信息（新增和更新的）
            List<I18nInfoDTO> i18nInfoToSync = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(i18nReferenceEntityList2Create)) {
                i18nInfoToSync.addAll(filterI18nInfoByReferences(i18nInfoList, i18nReferenceEntityList2Create));
            }
            if (CollectionUtils.isNotEmpty(i18nReferenceEntityList2Update)) {
                i18nInfoToSync.addAll(filterI18nInfoByReferences(i18nInfoList, i18nReferenceEntityList2Update));
            }

            if (CollectionUtils.isNotEmpty(i18nInfoToSync)) {
                // 同步到翻译工作台
                siteI18nQueryService.syncI18nInfoForSite(user.getTenantId(), null, i18nInfoToSync);
            }
        } catch (Exception e) {
            log.error("Failed to sync i18n info to translation workbench for site: {}", siteEntity.getApiName(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据站点apiName获取相关的文件信息列表
     *
     * @param siteApiName 站点apiName
     * @return 文件信息列表
     */
    private List<FileInfoVO> getFileInfoListBySiteApiName(User user, String siteApiName) {
        // 查询站点相关的文件引用关系
        List<ReferenceEntity> references = referenceEntityDao.findBySiteApiName(user.getTenantId(), siteApiName);
        if (CollectionUtils.isEmpty(references)) {
            return Collections.emptyList();
        }

        // 提取引用的文件apiName
        List<String> fileApiNames = references.stream()
                .filter(ref -> "file".equals(ref.getTargetType()))
                .map(ReferenceEntity::getTargetId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fileApiNames)) {
            return Collections.emptyList();
        }

        Map<String, ReferenceEntity> referenceEntityMap = references.stream()
                .collect(Collectors.toMap(ReferenceEntity::getTargetId, x -> x, (x1, x2) -> x1));

        // 批量查询文件信息
        List<FileEntity> fileEntities = cmsResourceService.findFileEntityByApiNames(user, fileApiNames);

        //批量计算url
        Map<String, String> fileUrl = cmsResourceService.getFileUrl(user, fileEntities);

        // 构建文件信息VO并生成访问URL
        List<FileInfoVO> result = new ArrayList<>();

        for (FileEntity fileEntity : fileEntities) {
            if (Objects.isNull(fileUrl)) {
                continue;
            }
            ReferenceEntity reference = referenceEntityMap.get(fileEntity.getApiName());
            if (Objects.isNull(reference)) {
                continue;
            }
            // 构建文件信息VO
            FileInfoVO fileInfoVO = new FileInfoVO();
            fileInfoVO.setApiName(fileEntity.getApiName());
            fileInfoVO.setSourceId(reference.getSourceId());
            fileInfoVO.setSourceType(reference.getSourceType());
            fileInfoVO.setWorkSpaceApiName(fileEntity.getWorkSpace());
            String url = fileUrl.get(fileEntity.getApiName());
            fileInfoVO.setUrl(StringUtils.defaultString(url, ""));
            result.add(fileInfoVO);
        }

        return result;
    }

    @Override
    public FindSiteResult findSiteByApiNameForView(String siteApiName, String clientType) {
        User user = RequestContextManager.getUser();
        int tenantId = user.getTenantId();
        SiteEntity siteEntity = siteEntityDao.findByApiName(tenantId, siteApiName);
        if (Objects.isNull(siteEntity)) {
            log.warn("site not found,tenantId:{},siteApiName:{}", tenantId, siteApiName);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_NOT_FOUND_ERROR, SOURCE_ERROR);
        }
        return findSiteForView(siteEntity, user, clientType);
    }

    @Override
    public FindSiteResult findSiteBySiteIdForView(String siteId, String clientType) {
        User user = RequestContextManager.getUser();
        int tenantId = user.getTenantId();
        SiteEntity siteEntity = siteEntityDao.findBySiteId(tenantId, siteId);
        if (Objects.isNull(siteEntity)) {
            log.warn("site not found,tenantId:{},siteId:{}", tenantId, siteId);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_NOT_FOUND_ERROR, SOURCE_ERROR);
        }
        return findSiteForView(siteEntity, user, clientType);
    }

    /**
     * 查找站点信息用于查看，抽取公共逻辑
     *
     * @param siteEntity 站点实体
     * @param user       用户信息
     * @param clientType 客户端类型
     * @return 站点查询结果
     */
    private FindSiteResult findSiteForView(SiteEntity siteEntity, User user, String clientType) {
        int tenantId = user.getTenantId();

        if (!hasSitePermission(siteEntity, user, clientType)) {
            return FindSiteResult.builder()
                    .permissionFlag(false)
                    .build();
        }
        if (siteEntity.unpublished(clientType)) {
            log.warn("site not published,tenantId:{},siteApiName:{}", tenantId, siteEntity.getApiName());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_NOT_PUBLISHED_ERROR, SOURCE_ERROR);
        }
        PaaSAppEntity paaSAppEntity = paaSAppDao.getPaaSAppByAppId(tenantId, siteEntity.getAppId());
        if (Objects.isNull(paaSAppEntity)) {
            log.warn("app not found,tenantId:{},appId:{}", tenantId, siteEntity.getAppId());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.APP_NOT_FOUND_ERROR, SOURCE_ERROR);
        }
        if (!Objects.equals(PaaSStatus.enable, paaSAppEntity.getStatus())) {
            log.warn("app not enable,tenantId:{},appId:{}", tenantId, siteEntity.getAppId());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.APP_NOT_ENABLE_ERROR, SOURCE_ERROR);
        }
        List<HomePageLayoutEntity> homePageLayoutEntityList = homePageLayoutDao.findBySiteApiName(tenantId,
                siteEntity.getAppId(), siteEntity.getApiName(), clientType);
        List<SitePageVO> sitePageVOList = homePageLayoutEntityList.stream().map(SitePageVO::of).collect(Collectors.toList());
        filterComponentByHomePageLayout(user, sitePageVOList, siteEntity.getAppId());

        List<String> themeLayoutApiNames = sitePageVOList.stream().map(SitePageVO::getThemeLayoutApiName).distinct()
                .collect(Collectors.toList());
        List<ThemeLayoutEntity> themeLayoutEntityList = themeLayoutEntityDao.findByApiNames(tenantId, themeLayoutApiNames, clientType);
        List<ThemeLayoutVO> themeLayoutVOList = themeLayoutEntityList.stream().map(ThemeLayoutVO::of).collect(Collectors.toList());
        filterComponentsByThemLayout(user, themeLayoutVOList, siteEntity.getAppId());

        List<ThemeStyleEntity> themeStyleEntityList = themeStyleEntityDao.findBySiteApiNameIncludeDisable(tenantId,
                siteEntity.getApiName(), clientType);
        List<ThemeStyleVO> themeStyleVOList = themeStyleEntityList.stream().map(ThemeStyleVO::of).collect(Collectors.toList());

        SiteConfigEntity siteConfigEntity = siteConfigEntityDao.findBySiteApiName(tenantId,
                siteEntity.getApiName(), clientType);
        SiteConfigVO siteConfigVO = Objects.isNull(siteConfigEntity) ? null : SiteConfigVO.of(siteConfigEntity);

        List<FileInfoVO> fileInfoVOList = getFileInfoListBySiteApiName(user, siteEntity.getApiName());

        // 查询多语信息（用户态只返回站点支持的语言）
        List<I18nInfoDTO> i18nInfoList = queryI18nInfoForSiteView(tenantId, siteEntity.getApiName(), siteEntity.getLangList());


        return FindSiteResult.builder()
                .siteInfo(SiteInfoVO.of(siteEntity))
                .themeLayoutList(themeLayoutVOList)
                .pageList(sitePageVOList)
                .siteConfig(siteConfigVO)
                .fileInfoList(fileInfoVOList)
                .themeStyleList(themeStyleVOList)
                .langList(siteEntity.getLangList())
                .i18nInfoList(i18nInfoList)
                .permissionFlag(true)
                .build();
    }

    /**
     * 通用的组件过滤方法，可以处理HomePageLayoutEntity和ThemeLayoutEntity
     *
     * @param user             用户
     * @param layoutEntityList 布局实体列表
     * @param appId            应用ID
     * @param layoutGetter     获取布局字符串的函数
     * @param layoutSetter     设置布局的函数
     * @param <T>              布局实体类型
     */
    private <T> void filterComponents(User user, List<T> layoutEntityList, String appId,
                                      Function<T, LayoutStructure> layoutGetter,
                                      BiConsumer<T, LayoutStructure> layoutSetter) {

        for (T layoutEntity : layoutEntityList) {
            LayoutStructure layoutStructure = layoutGetter.apply(layoutEntity);
            if (Objects.isNull(layoutStructure)) {
                continue;
            }
            List<Component> components = layoutStructure.getComponents();

            List<Component> componentList = components.stream()
                    .filter(x -> {
                        ComponentData componentData = x.getData();
                        if (Objects.isNull(componentData)) {
                            return true;
                        }
                        return FilterHandler.builder()
                                .user(user)
                                .wheres(componentData.getWheres())
                                .appId(appId)
                                .conditionAction(FilterHandler.ConditionAction.getByType(componentData.getConditionAction()))
                                .build()
                                .doFilter();
                    })
                    .collect(Collectors.toList());
            layoutStructure.setComponents(componentList);
            layoutSetter.accept(layoutEntity, layoutStructure);
        }
    }

    private void filterComponentByHomePageLayout(User user, List<SitePageVO> sitePageVOList, String appId) {
        filterComponents(user, sitePageVOList, appId,
                SitePageVO::getLayoutStructure,
                SitePageVO::setLayoutStructure);
    }

    private void filterComponentsByThemLayout(User user, List<ThemeLayoutVO> ThemeLayoutVOList, String appId) {
        filterComponents(user, ThemeLayoutVOList, appId,
                ThemeLayoutVO::getLayoutStructure,
                ThemeLayoutVO::setLayoutStructure);
    }

    /*
     * @description: 判断用户是否有站点权限
     * 处理逻辑:
     * 2. 站点适用范围
     * 3. 人的外部人员角色
     * 4. 应用适用范围
     * 5. 人员拥有的某一个角色 同时在 适用范围中, 而非人员
     *
     */
    private boolean hasSitePermission(SiteEntity siteEntity, User user, String clientType) {
        if (log.isDebugEnabled()) {
            log.debug("user:{},siteEntity:{}", user, siteEntity);
        }

        // 没传递外部企业id/外部人员id 就是 企业内部登录: 按照游客身份处理
        // 或者本身就是是游客身份登录
        // 如果已经登录, 判断条件不会被降级为游客, 故前置判断
        if (!user.isOutUser() || user.isOutGuestUser()) {
            return !siteEntity.needLogin();
        }

        // 站点适用范围
        Set<String> siteScope = CollectionUtils.emptyIfNull(siteEntity.getScopeListForCross()).stream()
                .map(ScopeForCross::getRoleId).collect(Collectors.toSet());
        // 人的外部人员角色
        // 不是游客才查询, 防止 Integer -> int 空指针
        QueryEmployeeScopeArg queryEmployeeScopeArg = QueryEmployeeScopeArg.builder()
                .employeeId(user.getUserId())
                .tenantId(user.getTenantId())
                .outUserId(user.getOutUserId())
                .outTenantId(user.getOutTenantId())
                .cross(true)
                .appId(null)
                .build();
        QueryEmployeeScopeResult scopeResult = webPageOutRoleService.queryUserScope(queryEmployeeScopeArg);
        List<Scope> scopes = Objects.isNull(scopeResult) ? Lists.newArrayList() : scopeResult.getScopeList();
        Set<String> userScope = CollectionUtils.emptyIfNull(scopes).stream()
                .map(Scope::getDataId).collect(Collectors.toSet());
        // 应用适用范围
        PaaSAppEntity paaSAppEntity = paaSAppDao.getPaaSAppByAppId(user.getTenantId(), siteEntity.getAppId());
        Set<String> appScope = Objects.isNull(paaSAppEntity) ? Sets.newHashSet()
                : CollectionUtils.emptyIfNull(paaSAppEntity.getScopeListForCross()).stream()
                .map(ScopeForCross::getRoleId).collect(Collectors.toSet());
        // 人员拥有的某一个角色, 角色 同时在 两个适用范围中, 而非两个角色分别在两个适用范围
        boolean inScope = userScope.stream().anyMatch(x -> siteScope.contains(x) && appScope.contains(x));
        if (log.isDebugEnabled()) {
            log.debug("inScope: {}, user Scope:{}, site ScopeList:{}, paaSApp ScopeList:{}", inScope, userScope, siteScope, appScope);
        }
        return inScope;
    }

    @Override
    public FindMenuByApiNameResult findMenuByApiName(int tenantId, FindMenuByApiNameArg arg) {
        TenantMenuEntity menuEntity = tenantMenuDao.findByApiName(tenantId, arg.getMenuApiName(), arg.getClientType());
        if (Objects.isNull(menuEntity)) {
            return new FindMenuByApiNameResult();
        }
        SiteMenuVO menu = SiteMenuVO.of(menuEntity);
        return FindMenuByApiNameResult.builder().menu(menu).build();
    }

    @Override
    public FindSiteConfigResult findSiteConfig(FindSiteConfigArg arg) {
        if (StringUtils.isBlank(arg.getUpstreamEa()) || StringUtils.isBlank(arg.getSiteId())) {
            log.warn("param is invalid,arg:{}", arg);
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_PARAM_ERROR, WebPageErrorCode.VALIDATION_ERROR.getCode());
        }
        int tenantId = eieaConverter.enterpriseAccountToId(arg.getUpstreamEa());
        SiteEntity siteEntity = siteEntityDao.findBySiteId(tenantId, arg.getSiteId());
        if (Objects.isNull(siteEntity)) {
            log.warn("site not found,tenantId:{},siteId:{}", tenantId, arg.getSiteId());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_NOT_FOUND_ERROR, SOURCE_ERROR.getCode());
        }
        if (siteEntity.unpublished(arg.getClientType())) {
            log.warn("site not published,tenantId:{},siteId:{}", tenantId, arg.getSiteId());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.SITE_NOT_PUBLISHED_ERROR, SOURCE_ERROR.getCode());
        }
        HomePageLayoutEntity pageEntity;
        if (StringUtils.isNotBlank(arg.getPageApiName())) {
            pageEntity = homePageLayoutDao.findBySiteApiNameAndPageApiName(tenantId, siteEntity.getAppId(),
                    siteEntity.getApiName(), arg.getPageApiName());
        } else {
            pageEntity = homePageLayoutDao.findHomePageBySiteApiName(tenantId, siteEntity.getAppId(), siteEntity.getApiName(), arg.getClientType());
        }
        if (Objects.isNull(pageEntity)) {
            log.warn("page not found,tenantId:{},appId:{},siteApiName:{},pageApiName:{}", tenantId, siteEntity.getAppId(),
                    siteEntity.getApiName(), arg.getPageApiName());
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PAGE_NOT_FOUND_ERROR, SOURCE_ERROR.getCode());
        }
        boolean needLogin = Objects.isNull(pageEntity.getNeedLogin()) ? siteEntity.needLogin() : pageEntity.getNeedLogin();
        return FindSiteConfigResult.builder().needLogin(needLogin).appId(siteEntity.getAppId()).build();
    }

    @Override
    public FindObjStatusByApiNamesResult findObjStatusByApiNames(int tenantId, String ea, String appId, FindObjStatusByApiNamesArg arg) {
        Set<String> argObjApiNames = arg.getObjApiNames();
        if (CollectionUtils.isEmpty(argObjApiNames)) {
            return FindObjStatusByApiNamesResult.builder().build();
        }
        // 查询对象信息
        Map<String, ObjectSimpleVo> objectDescribeMap = crmPassCacheManager.getSimpleObjectMapByApiNames(tenantId, Lists.newArrayList(argObjApiNames));
        // 应用绑定对象情况
        Set<String> appObjs = linkAppObjectAssociationDao.findByEaAndAppId(ea, appId).stream()
                .map(LinkAppObjectAssociationEntity::getObjectApiName)
                .collect(Collectors.toSet());
        Map<String, FindObjStatusByApiNamesResult.ObjStatus> objStatusMap = argObjApiNames.stream().collect(Collectors.toMap(x -> x,
                x -> {
                    ObjectSimpleVo describe = objectDescribeMap.getOrDefault(x, null);
                    return FindObjStatusByApiNamesResult.ObjStatus.builder()
                            .deleted(Objects.isNull(describe) || describe.isDeleted())
                            .disabled(Objects.isNull(describe) || describe.isDisabled())
                            .masterDetail(Objects.nonNull(describe) && describe.getMasterDetailObj())
                            .removed(!appObjs.contains(x))
                            .build();
                }));
        return FindObjStatusByApiNamesResult.builder().objStatus(objStatusMap).build();
    }

    @Override
    public FindSiteListResult findSiteList(FindSiteListArg arg) {
        if(Objects.isNull(arg)
                || Objects.isNull(arg.getTenantId())
                ||(StringUtils.isBlank(arg.getAppId())
                    && CollectionUtils.isEmpty(arg.getSiteApiList())
                    && CollectionUtils.isEmpty(arg.getSiteIdList()))){
            throw ValidateException.fromI18N(ErrorMessageI18NKey.PARAMS_ERROR);
        }
        int tenantId = RequestContextManager.getUser().getTenantId();
        List<SiteEntity> siteEntityList = siteEntityDao.findListSiteEntity(tenantId, query -> {
            query.field("status").in(Lists.newArrayList(PaaSStatus.enable, PaaSStatus.disable));
            if(Objects.nonNull(arg.getAppId())){
                query.field("appId").equal(arg.getAppId());
            }
            if(CollectionUtils.isNotEmpty(arg.getSiteApiList())){
                query.field("apiName").in(arg.getSiteApiList());
            }
            if(CollectionUtils.isNotEmpty(arg.getSiteIdList())){
                query.field("siteId").in(arg.getSiteIdList());
            }
        });
        List<SiteInfoPO> siteList = CollectionUtils.emptyIfNull(siteEntityList).stream()
                .filter(Objects::nonNull)
                .map(x -> toSiteInfoPO(x)).collect(Collectors.toList());
        FindSiteListResult result = new FindSiteListResult();
        result.setSiteList(siteList);
        return result;
    }

    private SiteInfoPO toSiteInfoPO(SiteEntity entity) {
        if(Objects.isNull(entity)){
            return null;
        }
        SiteInfoPO vo = new SiteInfoPO();
        vo.setId(entity.getId());
        vo.setApiName(entity.getApiName());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setSiteId(entity.getSiteId());
        vo.setNeedLogin(entity.getNeedLogin());
        vo.setAppId(entity.getAppId());
        vo.setStatus(entity.getStatus());
        vo.setCreatorId(entity.getCreatorId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdaterId(entity.getUpdaterId());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setPublishStatus(entity.getPublishStatus());
        vo.setPublishTime(entity.getPublishTime());
        vo.setPublisherId(entity.getPublisherId());
        vo.setPublishVersion(entity.getPublishVersion());
        vo.setAppPublishStatus(entity.getAppPublishStatus());
        vo.setAppPublishTime(entity.getAppPublishTime());
        vo.setAppPublisherId(entity.getAppPublisherId());
        vo.setAppPublishVersion(entity.getAppPublishVersion());
        return vo;
    }

    @Override
    public DataSourceTypeResult getDataSourceType(User user, Locale locale) {
        List<DataSource> dataSources = uiPaaSConfig.getDataSources();

        locale = Objects.isNull(locale) ? Locale.CHINA : locale;
        Map<String, String> transValue = TranslateI18nUtils.getTransValueByKeys(
                user.getTenantId(), locale,
                dataSources.stream()
                        .map(DataSource::getI18n).filter(Objects::nonNull)
                        .collect(Collectors.toList()));
        dataSources.forEach(dataSource ->
                dataSource.setName(StringUtils.firstNonBlank(transValue.get(dataSource.getI18n()), dataSource.getName())));

        return DataSourceTypeResult.builder()
                .dataSources(dataSources)
                .build();
    }
}
