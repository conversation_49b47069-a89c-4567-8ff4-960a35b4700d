package com.facishare.webpage.customer.controller.model.result.portal;

import com.facishare.webpage.customer.config.model.DataSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2024/11/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DataSourceTypeResult {
    private List<DataSource> dataSources;
}
