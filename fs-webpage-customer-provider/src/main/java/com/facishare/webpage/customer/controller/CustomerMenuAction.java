package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.QueryCustomerMenuListArg;
import com.facishare.webpage.customer.controller.model.arg.customer.*;
import com.facishare.webpage.customer.controller.model.result.customer.*;

/**
 * Created by zhangyu on 2020/11/3
 */
public interface CustomerMenuAction {
    /**
     * 新建自定义菜单项
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    SaveCustomerMenuResult saveCustomerMenu(UserInfo userInfo,
                                            ClientInfo clientInfo,
                                            SaveCustomerMenuArg arg);

    /**
     * 更新自定义菜单项
     *
     * @param userInfo
     * @param arg
     * @return
     */
    UpdateCustomerMenuResult updateCustomerMenu(UserInfo userInfo,
                                                ClientInfo clientInfo,
                                                UpdateCustomerMenuArg arg);

    /**
     * 删除自定义菜单项
     *
     * @param userInfo
     * @param arg
     * @return
     */
    DeleteCustomerMenuResult deleteCustomerMenu(UserInfo userInfo,
                                                DeleteCustomerMenuArg arg);

    /**
     * 根据apiName获取自定义菜单项
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetCustomerMenuByApiNameResult getCustomerMenuByApiName(UserInfo userInfo,
                                                            ClientInfo clientInfo,
                                                            GetCustomerMenuByApiNameArg arg);

    /**
     * 获取自定义菜单项
     *
     * @param userInfo
     * @param clientInfo
     * @param arg
     * @return
     */
    GetCustomerMenuListResult getCustomerMenuList(UserInfo userInfo,
                                                  ClientInfo clientInfo,
                                                  GetCustomerMenuListArg arg);

    /**
     * 获取自定义页面数据
     *
     * @param userInfo
     * @return
     */
    GetCustomerLayoutsResult getCustomerLayouts(UserInfo userInfo,
                                                ClientInfo clientInfo,
                                                GetCustomerMenuListArg arg);

    /**
     * 获取互联应用列表
     *
     * @param userInfo
     * @return
     */
    GetCrossAppsResult getCrossApps(UserInfo userInfo, ClientInfo clientInfo, GetCrossAppsArg arg);

    /**
     * 查看自定义页面分配的视图
     *
     * @param userInfo
     * @param clientInfo
     * @param queryCustomerMenuListArg
     * @return
     */
    GetCustomerMenuListResult queryCustomerMenuItemsByPageTemplateId(UserInfo userInfo,
                                                                     ClientInfo clientInfo,
                                                                     QueryCustomerMenuListArg queryCustomerMenuListArg);

}
