package com.facishare.webpage.customer.dao.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/9/16.
 */
@Embedded
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuDataEntity implements Serializable{

    @Property("orderNumber")
    private Integer orderNumber;

    @Property("apiName")
    private String apiName;

    @Property("appId")
    private String appId;

    @Property("type")
    private String type;

    @Property("name")
    private String name;

    @Property("groupApiName")
    private String groupApiName;

    @Property("isHidden")
    private Boolean isHidden;

    @Property("metaType")
    private String metaType;    // 站点主导航菜单项类型

    @Property("data")
    private JSONObject data;

    @Property("icons")
    private List<JSONObject> icons;

}
