package com.facishare.webpage.customer.controller;

import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.controller.model.arg.portal.*;
import com.facishare.webpage.customer.controller.model.result.CommonResult;
import com.facishare.webpage.customer.controller.model.result.portal.DataSourceTypeResult;
import com.facishare.webpage.customer.controller.model.result.portal.FindSiteInfosByAppIdResult;
import com.facishare.webpage.customer.controller.model.result.portal.FindSiteResult;
import com.facishare.webpage.customer.controller.model.result.portal.SiteDraftResult;

/**
 * Created by zhouwr on 2024/11/6.
 */
public interface SiteManagerAction {
    CommonResult createSiteInfo(UserInfo userInfo, ClientInfo clientInfo, CreateSiteInfoArg arg);

    CommonResult updateSiteInfo(UserInfo userInfo, ClientInfo clientInfo, UpdateSiteInfoArg arg);

    FindSiteInfoResult findSiteInfo(UserInfo userInfo, ClientInfo clientInfo, FindSiteInfoArg arg);

    SiteDraftResult updateSitePages(UserInfo userInfo, ClientInfo clientInfo, UpdateSitePagesArg arg);

    SiteDraftResult publishSiteDraft(UserInfo userInfo, ClientInfo clientInfo, PublishSiteDraftArg arg);

    FindSiteInfosByAppIdResult findSiteInfosByAppId(UserInfo userInfo, ClientInfo clientInfo, FindSiteInfosByAppIdArg arg);

    FindSiteResult findSiteByApiName(UserInfo userInfo, ClientInfo clientInfo, FindSiteByApiNameArg arg);


    DataSourceTypeResult getDataSourceType(UserInfo userInfo, ClientInfo clientInfo);
}
