package com.facishare.webpage.customer.config.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @Data : 2025/7/29
 * @Description : 互联站点多级导航数据源类型的菜单可选类别定义
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataSource {
    private String id;
    private String name;
    private String i18n;

    public DataSource copy(){
        return DataSource.builder()
                .id(this.getId())
                .name(this.getName())
                .i18n(this.getI18n())
                .build();
    }
}
