package com.facishare.webpage.customer.controller.model.result.homepage;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 客服工作台 适用应用/渠道分类 的可用数据
 * <AUTHOR> ZhenHui
 * @Date : 2025/1/7
 */
@Data
public class GetWorkTableConfigResult implements Serializable {

    private WorkTableConfigDataVO workTableConfig;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkTableConfigDataVO implements Serializable {
        @JSONField(ordinal = 1)
        private List<AppVO> app = Collections.emptyList();
        @JSONField(ordinal = 2)
        private List<ChannelVO> channel = Collections.emptyList();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppVO implements Serializable {
        @JSONField(ordinal = 1)
        private String appId;

        @JSONField(ordinal = 2)
        private String appName;

        @JSONField(ordinal = 3)
        private String nameI18n;

        @JSONField(ordinal = 4)
        private Integer order = Integer.MAX_VALUE;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelVO implements Serializable {
        @JSONField(ordinal = 1)
        private String channelId;

        @JSONField(ordinal = 2)
        private String channelName;

        @JSONField(ordinal = 3)
        private String nameI18n;

        @JSONField(ordinal = 4)
        private List<String> appIds = Collections.emptyList();

        @JSONField(ordinal = 5)
        private Integer order = Integer.MAX_VALUE;
    }
} 