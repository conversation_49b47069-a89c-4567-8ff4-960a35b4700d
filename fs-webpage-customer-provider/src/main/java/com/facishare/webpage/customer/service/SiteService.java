package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.api.model.arg.FindSiteConfigArg;
import com.facishare.webpage.customer.api.model.result.FindSiteConfigResult;
import com.facishare.webpage.customer.api.model.arg.FindSiteListArg;
import com.facishare.webpage.customer.api.model.result.FindSiteListResult;
import com.facishare.webpage.customer.controller.model.arg.portal.*;
import com.facishare.webpage.customer.controller.model.result.portal.FindMenuByApiNameResult;
import com.facishare.webpage.customer.controller.model.result.portal.FindObjStatusByApiNamesResult;
import com.facishare.webpage.customer.controller.model.result.portal.FindSiteResult;
import com.facishare.webpage.customer.controller.model.result.portal.SiteInfoVO;
import com.facishare.webpage.customer.controller.model.result.portal.DataSourceTypeResult;
import com.facishare.webpage.customer.dao.entity.SiteDraftEntity;

import java.util.List;
import java.util.Locale;

/**
 * Created by zhouwr on 2024/11/5.
 */
public interface SiteService {
    SiteDraftEntity publishSiteDraft(User user, PublishSiteDraftArg arg);

    void createSiteInfo(User user, SiteInfoDTO siteInfo);

    SiteInfoVO findSiteInfo(User user, String siteApiName);

    void updateSiteInfo(User user, SiteInfoDTO siteInfo);

    SiteDraftEntity updateSitePages(User user, UpdateSitePagesArg arg);

    void enableSite(User user, String siteApiName);

    void disableSite(User user, String siteApiName);

    void deleteSite(User user, String siteApiName);

    List<SiteInfoVO> findSitesByAppId(int tenantId, String appId);

    FindSiteResult findSiteByApiNameForManager(String siteApiName, String clientType);

    FindSiteResult findSiteByApiNameForView(String siteApiName, String clientType);

    FindSiteResult findSiteBySiteIdForView(String siteId, String clientType);

    FindMenuByApiNameResult findMenuByApiName(int tenantId, FindMenuByApiNameArg arg);

    FindSiteConfigResult findSiteConfig(FindSiteConfigArg arg);

    FindObjStatusByApiNamesResult findObjStatusByApiNames(int tenantId,  String ea, String appId, FindObjStatusByApiNamesArg arg);

    FindSiteListResult findSiteList(FindSiteListArg arg);

    DataSourceTypeResult getDataSourceType(User user, Locale locale);
}
