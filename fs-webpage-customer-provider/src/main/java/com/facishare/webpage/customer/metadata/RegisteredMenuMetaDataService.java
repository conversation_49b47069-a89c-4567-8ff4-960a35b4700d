package com.facishare.webpage.customer.metadata;

import com.facishare.qixin.common.monitor.GlobalStopWatch;
import com.facishare.qixin.common.monitor.SlowLog;
import com.facishare.webpage.customer.api.model.arg.QueryMenusByCollectionIds;
import com.facishare.webpage.customer.dao.entity.MenuEntity;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataArg;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataResult;
import com.facishare.webpage.customer.metadata.model.RegisteredCustomerMenuData;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>ui
 * @Data : 2025/7/21
 * @Description :
 */
@Service("registeredMenuMetaDataService")
public class RegisteredMenuMetaDataService implements MetaDataService {

    @Resource
    private InternalMenusRegisterService internalMenusRegisterService;

    @Override
    public QueryMetaDataResult queryMetaData(QueryMetaDataArg arg) {
        int tenantId = arg.getTenantId();
        QueryMenusByCollectionIds.Arg queryArg = QueryMenusByCollectionIds.Arg.builder()
                .tenantId(tenantId)
                .build();
        SlowLog slowLog = GlobalStopWatch.create("queryRegisteredMenuMetaData", 100L);
        List<MenuEntity> registerMenuDataList = internalMenusRegisterService.queryMenusByTenantId(queryArg);
        slowLog.lap("RegisteredMenuMetaData end");
        slowLog.stop("queryRegisteredMenuMetaData");
        return QueryMetaDataResult.builder().metaMenuDataList(registerMenuDataList.stream()
                .filter(Objects::nonNull)
                .map(entity -> RegisteredCustomerMenuData.of(entity, arg.getOldAppId()))
                .collect(Collectors.toList())).build();
    }

    public List<MetaMenuData> queryMetaDataFormCollectionId(QueryMetaDataArg arg, String collectionId) {
        if(StringUtils.isBlank(collectionId)){
            return Lists.newArrayList();
        }
        int tenantId = arg.getTenantId();
        QueryMenusByCollectionIds.Arg queryByCollectionIdArg = QueryMenusByCollectionIds.Arg.builder()
                .collectionIds(Lists.newArrayList(collectionId))
                .tenantId(tenantId)
                .build();
        List<MenuEntity> registerMenuDataList = internalMenusRegisterService.queryMenusByTenantId(queryByCollectionIdArg);
        return registerMenuDataList.stream()
                .filter(Objects::nonNull)
                .map(entity -> RegisteredCustomerMenuData.of(entity, arg.getOldAppId()))
                .collect(Collectors.toList());
    }
}
