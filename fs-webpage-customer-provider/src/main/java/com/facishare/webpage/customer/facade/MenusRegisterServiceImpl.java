package com.facishare.webpage.customer.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.webpage.customer.api.exception.ParameterException;
import com.facishare.webpage.customer.api.model.arg.*;
import com.facishare.webpage.customer.api.model.core.*;
import com.facishare.webpage.customer.api.utils.I18NKey;
import com.facishare.webpage.customer.dao.MenuEntityDao;
import com.facishare.webpage.customer.dao.entity.MenuEntity;
import com.facishare.webpage.customer.metadata.InternalMenusRegisterService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.mongodb.morphia.query.Query;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by <PERSON><PERSON> on 19/12/16.
 */

public class MenusRegisterServiceImpl implements InternalMenusRegisterService {

    @Resource
    private MenuEntityDao menuEntityDao;


    private MenuEntity buildMenuEntity(int tenantId, String collectionId, String source, Menu menu) {
        MenuEntity menuEntity = new MenuEntity();
        menuEntity.setTenantId(tenantId);
        menuEntity.setCollectionId(collectionId);
        menuEntity.setMenuId(menu.getId());
        menuEntity.setName(menu.getName());
        menuEntity.setNameI18Key(menu.getNameI18nKey());

        TenantPrivilege tenantPrivilege = menu.getTenantPrivilege();
        String tenantPrivilegeStr = tenantPrivilege == null ? null : JSON.toJSONString(tenantPrivilege);
        menuEntity.setTenantPrivilege(tenantPrivilegeStr);

        PersonPrivilege personPrivilege = menu.getPersonPrivilege();
        String personPrivilegeStr = personPrivilege == null ? null : JSON.toJSONString(personPrivilege);
        menuEntity.setPersonPrivilege(personPrivilegeStr);

        Icon icon = menu.getIcon();
        String iconStr = icon == null ? null : JSON.toJSONString(icon);
        menuEntity.setIcon(iconStr);

        Url url = menu.getUrl();
        String urlStr = url == null ? null : JSON.toJSONString(url);
        menuEntity.setUrl(urlStr);

        menuEntity.setDeviceTypes(menu.getDeviceTypes());
        menuEntity.setSource(source);
        menuEntity.setCreateTime(System.currentTimeMillis());
        menuEntity.setApplyType(null == menu.getApplyType() ? 0 : menu.getApplyType());
        if (StringUtils.isNotBlank(menu.getAppId())) {
            menuEntity.setAppId(menu.getAppId());
        }
        return menuEntity;
    }

    private Menu buildMenu(MenuEntity entity) {
        Menu menu = new Menu();
        menu.setId(entity.getMenuId());
        menu.setName(entity.getName());
        menu.setNameI18nKey(entity.getNameI18Key());
        menu.setDeviceTypes(entity.getDeviceTypes());

        String tenantPrivilegeStr = entity.getTenantPrivilege();
        TenantPrivilege tenantPrivilege = Strings.isNullOrEmpty(tenantPrivilegeStr) ? null : JSON.parseObject(tenantPrivilegeStr, TenantPrivilege.class);
        menu.setTenantPrivilege(tenantPrivilege);

        String personPrivilegeStr = entity.getPersonPrivilege();
        PersonPrivilege personPrivilege = Strings.isNullOrEmpty(personPrivilegeStr) ? null : JSON.parseObject(personPrivilegeStr, PersonPrivilege.class);
        menu.setPersonPrivilege(personPrivilege);

        String urlStr = entity.getUrl();
        Url url = Strings.isNullOrEmpty(urlStr) ? null : JSON.parseObject(urlStr, Url.class);
        menu.setUrl(url);

        String iconStr = entity.getIcon();
        Icon icon = Strings.isNullOrEmpty(iconStr) ? null : JSON.parseObject(iconStr, Icon.class);
        menu.setIcon(icon);

        return menu;
    }

    private List<MenuEntity> buildMenuEntityList(int tenantId, String collectionId, String source, List<Menu> menus) {
        List<MenuEntity> menuEntities = menus.stream().map(menu -> buildMenuEntity(tenantId, collectionId, source, menu)).collect(Collectors.toList());
        return menuEntities;
    }


    void checkCreateMenusParam(CreateMenus.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getCollectionId())) {
            throw new ParameterException(I18N.text(I18NKey.NOT_NULL, "collectionId"));
        }
        if (CollectionUtils.isEmpty(arg.getMenus())) {
            throw new ParameterException(I18N.text(I18NKey.NOT_NULL, "menus"));
        }

        for (Menu menu : arg.getMenus()) {
            if (Strings.isNullOrEmpty(menu.getId())
                    || Strings.isNullOrEmpty(menu.getName())
                    || menu.getUrl() == null
                    || menu.getIcon() == null) {
                throw new ParameterException(I18N.text(I18NKey.MENU ));
            }
        }
    }

    @Override
    public CreateMenus.Result createMenus(CreateMenus.Arg arg) {
        checkCreateMenusParam(arg);

        List<MenuEntity> buildMenuEntityList = buildMenuEntityList(arg.getTenantId(), arg.getCollectionId(), arg.getSource(), arg.getMenus());
        menuEntityDao.save(buildMenuEntityList);
        return new CreateMenus.Result();
    }

    @Override
    public DeleteMenus.Result deleteMenus(DeleteMenus.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getCollectionId())) {
            return new DeleteMenus.Result();
        }
        menuEntityDao.deleteMenus(arg.getTenantId(), arg.getCollectionId(), arg.getMenuIdList());
        return new DeleteMenus.Result();
    }

    @Override
    public UpdateMenus.Result updateMenus(UpdateMenus.Arg arg) {
        List<Menu> menus = arg.getMenus();
        List<MenuEntity> menuEntities = menus.stream().map(menu -> buildMenuEntity(arg.getTenantId(), arg.getCollectionId(), null, menu)).collect(Collectors.toList());
        for (MenuEntity menuEntity : menuEntities) {
            menuEntityDao.findAndModify(menuEntity);
        }
        return new UpdateMenus.Result();
    }

    @Override
    public QueryMenusByCollectionIds.Result queryMenusByCollectionIds(QueryMenusByCollectionIds.Arg arg) {
        List<MenuEntity> menuEntities = menuEntityDao.queryMenus(arg.getTenantId(), arg.getCollectionIds());
        if (CollectionUtils.isEmpty(menuEntities)) {
            return new QueryMenusByCollectionIds.Result();
        }
        Map<String, List<Menu>> menus = new HashMap<>();
        menuEntities.stream().forEach(menuEntity -> {
            List<Menu> menuList = menus.get(menuEntity.getCollectionId());
            if (menuList == null) {
                menuList = Lists.newArrayList();
                menus.put(menuEntity.getCollectionId(), menuList);
            }
            menuList.add(buildMenu(menuEntity));
        });
        QueryMenusByCollectionIds.Result result = new QueryMenusByCollectionIds.Result();
        result.setMenus(menus);
        return result;
    }

    @Override
    public QueryMenus.Result queryMenus(QueryMenus.Arg arg) {
        List<MenuEntity> menuEntities = menuEntityDao.queryMenusByMenuIds(arg.getTenantId(), arg.getMenuIds());
        QueryMenus.Result result = new QueryMenus.Result();
        List<Menu> menus = menuEntities.stream().map(entity -> buildMenu(entity)).collect(Collectors.toList());
        result.setMenus(menus);
        return result;
    }

    @Override
    public List<MenuEntity> queryMenusByTenantId(QueryMenusByCollectionIds.Arg arg) {
        List<MenuEntity> menuEntities = menuEntityDao.queryMenusByTenantId(arg.getTenantId(), (Query<MenuEntity> query) -> {
            query.or(
                    query.criteria("menuType").doesNotExist(),
                    query.criteria("menuType").equal(null),
                    query.criteria("menuType").equal(0)
            );
            Optional.ofNullable(arg.getCollectionIds())
                    .ifPresent(x -> x.removeIf(StringUtils::isBlank));
            if(!CollectionUtils.isEmpty(arg.getCollectionIds())) {
                query.and(
                        query.criteria("collectionId").in(arg.getCollectionIds())
                );
            }
        });
        return menuEntities.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}



