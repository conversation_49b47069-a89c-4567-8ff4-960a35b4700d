package com.facishare.webpage.customer.drop;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.constant.Constant;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.model.DataSourceEnv;
import com.facishare.webpage.customer.api.model.Scope;
import com.facishare.webpage.customer.api.model.arg.QueryMenusByCollectionIds;
import com.facishare.webpage.customer.api.utils.UiPaasParallelUtils;
import com.facishare.webpage.customer.common.CheckService;
import com.facishare.webpage.customer.common.OrganizationCommonService;
import com.facishare.webpage.customer.constant.MenuType;
import com.facishare.webpage.customer.constant.WebPageConstants;
import com.facishare.webpage.customer.core.business.ComponentListManager;
import com.facishare.webpage.customer.core.model.ComponentDto;
import com.facishare.webpage.customer.core.model.ComponentTypConst;
import com.facishare.webpage.customer.core.model.DropListItem;
import com.facishare.webpage.customer.core.model.MenuSourceTypeConst;
import com.facishare.webpage.customer.designer.DropListService;
import com.facishare.webpage.customer.designer.model.DesignerAuthInfo;
import com.facishare.webpage.customer.designer.model.DropListType;
import com.facishare.webpage.customer.designer.model.StandGetDropList;
import com.facishare.webpage.customer.drop.model.GetMenuDropList;
import com.facishare.webpage.customer.facade.MenusRegisterServiceImpl;
import com.facishare.webpage.customer.metadata.CustomerPageMetaDataService;
import com.facishare.webpage.customer.metadata.MetaMenuService;
import com.facishare.webpage.customer.metadata.ObjectMetaDataService;
import com.facishare.webpage.customer.metadata.RegisteredMenuMetaDataService;
import com.facishare.webpage.customer.metadata.factory.PermissionFactory;
import com.facishare.webpage.customer.metadata.model.MetaMenuData;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataArg;
import com.facishare.webpage.customer.metadata.model.QueryMetaDataResult;
import com.facishare.webpage.customer.model.component.GroupDropItem;
import com.facishare.webpage.customer.model.component.MenuDropListItem;
import com.facishare.webpage.customer.service.RemoteCrossService;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 */
@Component
@Slf4j
public class MenuDropListServiceService extends DropListService<GetMenuDropList.Arg, StandGetDropList.Result> {

    @Resource
    private ComponentListManager componentListManager;
    @Resource
    private MetaMenuService metaMenuService;
    @Resource
    private RemoteCrossService remoteCrossService;
    @Resource
    private ObjectMetaDataService objectMetaDataService;
    @Resource
    private CustomerPageMetaDataService customerPageMetaDataService;
    @Resource
    private PermissionFactory permissionFactory;
    @Resource
    private OrganizationCommonService organizationCommonService;
    @Resource
    private CheckService checkService;
    @Resource
    private MenusRegisterServiceImpl menusRegisterService;
    @Resource
    private RegisteredMenuMetaDataService registeredMenuMetaDataService;

    @Override
    public DropListType getDropListType() {
        return DropListType.MENU;
    }

    @Override
    protected StandGetDropList.Result doGetDropList(GetMenuDropList.Arg arg, DesignerAuthInfo designerAuthInfo) {
        UserInfo userInfo = designerAuthInfo.getUserInfo();
        String bizId = getBizIdWithUserInfo(userInfo, arg);
        log.info("use config: {}", bizId + "-web-menu");
        List<ComponentDto> componentDtos = componentListManager.getComponentDtoListByAppId(
                bizId + "-web-menu",
                userInfo,
                (List<com.facishare.webpage.customer.core.model.Component> components) -> {
                    List<String> collectIds = components.stream().
                            filter(componentDto -> ComponentTypConst.MENU_COLLECTION_TYPE == componentDto.getComponentType())
                            .map(componentDto -> componentDto.getCollectionId())
                            .distinct()
                            .collect(Collectors.toList());
                    QueryMenusByCollectionIds.Arg queryArg = new QueryMenusByCollectionIds.Arg();
                    queryArg.setTenantId(userInfo.getEnterpriseId());
                    queryArg.setCollectionIds(collectIds);
                    QueryMenusByCollectionIds.Result result =  menusRegisterService.queryMenusByCollectionIds(queryArg);
                    return Objects.isNull(result) ? Maps.newHashMap() : result.getMenus();
                }
        );
        if (CollectionUtils.isEmpty(componentDtos)) {
            return StandGetDropList.Result.builder().build();
        }
        Integer enterpriseId = userInfo.getEnterpriseId();
        Locale locale = designerAuthInfo.getLocale();
        //设置 菜单项/菜单分组 的多语
        setComponentDtoLanguage(enterpriseId, componentDtos, locale);
        //covert ComponentDto -> DropListItem
        List<DropListItem> dropListItems = covertMenuDropListItemList(arg.getEnv(),
                userInfo, bizId, componentDtos, arg.isNeedFilterPermission(), locale, arg.getCustomerLinkAppId(), arg.isPreviewNewCrmFlag());
        return StandGetDropList.Result.builder().
                dropListItemList(dropListItems).
                build();
    }

    @Override
    protected void before(GetMenuDropList.Arg arg) {
        super.before(arg);
        if (StringUtils.isEmpty(arg.getBizId())) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
    }

    private List<DropListItem> covertMenuDropListItemList(DataSourceEnv env,
                                                          UserInfo userInfo,
                                                          String bizId,
                                                          List<ComponentDto> componentDtos,
                                                          boolean needFilterPermission,
                                                          Locale locale,
                                                          String customerLinkAppId,
                                                          boolean previewNewCrmFlag) {
        if (CollectionUtils.isEmpty(componentDtos)) {
            return Lists.newArrayList();
        }
        Integer enterpriseId = userInfo.getEnterpriseId();
        AtomicReference<List<String>> openAppIdsAtomic = new AtomicReference<>();
        ParallelUtils.ParallelTask parallelTask = UiPaasParallelUtils.createParallelTask(String.valueOf(userInfo.getEnterpriseId()));
        parallelTask.submit(MonitorTaskWrapper.wrap(() -> {
            List<SimpleLinkAppResult> upSimpleLinkApp = remoteCrossService.getUpSimpleLinkApp(enterpriseId, locale);
            List<String> appIds = upSimpleLinkApp.stream().map(SimpleLinkAppResult::getAppId).collect(Collectors.toList());
            openAppIdsAtomic.set(appIds);
        }));
        AtomicReference<List<Scope>> scopeListAtomic = new AtomicReference<>();
        parallelTask.submit(MonitorTaskWrapper.wrap(() -> {
            List<Scope> scopes = organizationCommonService.queryScopeList(enterpriseId, userInfo.getEmployeeId(), null, null, bizId);
            scopeListAtomic.set(scopes);
        }));

        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("covertMenuDropListItemList error by userInfo:{}, bizId:{}, needFilterPermission:{}", userInfo, bizId, needFilterPermission, e);
        }

        return getDropListItems(env, userInfo, bizId, componentDtos, needFilterPermission,
                locale, enterpriseId, openAppIdsAtomic.get() == null ? Lists.newArrayList() : openAppIdsAtomic.get(),
                scopeListAtomic.get(), customerLinkAppId, previewNewCrmFlag);
    }

    @NotNull
    private List<DropListItem> getDropListItems(DataSourceEnv env,
                                                UserInfo userInfo,
                                                String bizId,
                                                List<ComponentDto> componentDtos,
                                                boolean needFilterPermission,
                                                Locale locale,
                                                Integer enterpriseId,
                                                List<String> openAppIds,
                                                List<Scope> scopeList,
                                                String customerLinkAppId,
                                                boolean previewNewCrmFlag) {
        List<DropListItem> dropListItemList = Lists.newCopyOnWriteArrayList();
        ParallelUtils.ParallelTask parallelTask = UiPaasParallelUtils.createParallelTask(String.valueOf(enterpriseId));
            for (ComponentDto componentDto : componentDtos) {
            switch (componentDto.getComponentType()) {
                case ComponentTypConst.GROUP_TYPE:
                    dropListItemList.add(buildGroupDropListItem(componentDto));
                    break;
                case ComponentTypConst.MENU_COLLECTION_TYPE:
                    String parentId = componentDto.getParentId();
                    parallelTask.submit(MonitorTaskWrapper.wrap(() -> {
                        List<MetaMenuData> metaMenuDataList = filterMetaMenuDataList(env, userInfo, bizId, openAppIds,
                                scopeList, componentDto, needFilterPermission, locale, customerLinkAppId, previewNewCrmFlag);
                        dropListItemList.addAll(buildMenuDropListItem(parentId, metaMenuDataList));
                    }));
                    break;
                default:
                    break;
            }
        }
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("covertMenuDropListItemList error by enterpriseId:{}, appId:{}", enterpriseId, bizId, e);
        }
        return dropListItemList;
    }

    private List<MetaMenuData> filterMetaMenuDataList(DataSourceEnv env,
                                                      UserInfo userInfo,
                                                      String bizId,
                                                      List<String> openAppIds,
                                                      List<Scope> scopeList,
                                                      ComponentDto componentDto,
                                                      boolean needFilterPermission,
                                                      Locale locale,
                                                      String customerLinkAppId,
                                                      boolean previewNewCrmFlag) {
        // componentDto转化为metaMenuDataList时存在权限过滤
        List<MetaMenuData> metaMenuDataList = queryMenus(env, userInfo.getEnterpriseId(), bizId, openAppIds, componentDto, locale, customerLinkAppId, previewNewCrmFlag);
        if (!needFilterPermission) {
            return metaMenuDataList;
        }
        Pair<List<MetaMenuData>, Map<String, List<String>>> pair =
                permissionFactory.filterMetaMenuDataList(bizId, userInfo, null, metaMenuDataList, "WEB");
        List<MetaMenuData> filterMetaMenuDataList = pair.getLeft();
        Map<String, List<String>> funcMap = pair.getRight();
        return filterMetaMenuDataList.stream().
                filter(x -> {
                    List<String> func = funcMap.get(x.getApiName());
                    if (x.getFunctions() == null || CollectionUtils.isNotEmpty(x.getFunctions())) {
                        return func.contains(WebPageConstants.FUNC_CODE_LIST);
                    } else {
                        return true;
                    }
                }).
                filter(x -> {
                    if (!MenuType.CUSTOMER_MENU.equals(x.getMenuType())) {
                        return true;
                    }
                    return CollectionUtils.containsAny(x.getScopeList(), scopeList);
                }).collect(Collectors.toList());
    }

    private List<MetaMenuData> queryMenus(DataSourceEnv env,
                                          int tenantId,
                                          String appId,
                                          List<String> openAppIds,
                                          ComponentDto componentDto,
                                          Locale locale,
                                          String customerLinkAppId,
                                          boolean previewNewCrmFlag) {
        if (componentDto == null) {
            return Lists.newArrayList();
        }

        if (componentDto.getTenantPrivilege() != null && StringUtils.isNotEmpty(componentDto.getTenantPrivilege().getAppId())) {
            appId = componentDto.getTenantPrivilege().getAppId();
        }
        String formAppId = StringUtils.isNotBlank(customerLinkAppId) ? customerLinkAppId : appId;
        if (DataSourceEnv.CROSS == env && !openAppIds.contains(formAppId)) {
            return Lists.newArrayList();
        }
        switch (componentDto.getMenuSourceType()) {
            case MenuSourceTypeConst.CRM_MENU_TYPE:
                QueryMetaDataArg arg = QueryMetaDataArg.builder().
                        tenantId(tenantId).
                        dataSourceEnv(env).
                        appId(appId).
                        oldAppId(appId).
                        menuType(componentDto.getType()).
                        previewNewCrmFlag(previewNewCrmFlag).
                        locale(locale).build();
                QueryMetaDataResult result = objectMetaDataService.queryMetaData(arg);
                if (result == null) {
                    return Lists.newArrayList();
                }
                return result.getMetaMenuDataList();
            case MenuSourceTypeConst.PAGE_MENU_TYPE:    // 自定义页面
                QueryMetaDataArg queryPageMetaDataArg = QueryMetaDataArg.builder().
                        dataSourceEnv(env).
                        locale(locale).
                        tenantId(tenantId).
                        oldAppId(formAppId).build();
                QueryMetaDataResult pageResult = customerPageMetaDataService.queryMetaData(queryPageMetaDataArg);
                if (pageResult == null) {
                    return Lists.newArrayList();
                }
                return pageResult.getMetaMenuDataList();
            case MenuSourceTypeConst.CONFIG_MENU_TYPE:
                return metaMenuService.filterConfigMenus(env, tenantId, appId, componentDto.getMenus(), locale, previewNewCrmFlag);
            case MenuSourceTypeConst.CUSTOMER_MENU_TYPE:
                return metaMenuService.getCustomerMenuDataList(env, tenantId, formAppId, componentDto.getType(), locale);
            case MenuSourceTypeConst.CUSTOMER_LINK_APP_OBJECT_TYPE:
                return objectMetaDataService.getCustomerLinkAppObjects(env, tenantId, appId, componentDto.getType(), locale, customerLinkAppId);
            case MenuSourceTypeConst.DYNAMIC_CUSTOMER_MENU_TYPE:
                QueryMetaDataArg queryDynamicMenuMetaDataArg = QueryMetaDataArg.builder().
                        dataSourceEnv(env).
                        locale(locale).
                        tenantId(tenantId).
                        oldAppId(formAppId).build();
                return registeredMenuMetaDataService.queryMetaDataFormCollectionId(queryDynamicMenuMetaDataArg, componentDto.getId());
            default:
                break;
        }
        return Lists.newArrayList();
    }

    private DropListItem buildGroupDropListItem(ComponentDto componentDto) {
        GroupDropItem groupDropItem = new GroupDropItem();
        groupDropItem.setComponentDto(componentDto);
        return groupDropItem.buildDropListItem();
    }

    private List<DropListItem> buildMenuDropListItem(String parentId, List<MetaMenuData> metaMenuDataList) {
        if (CollectionUtils.isEmpty(metaMenuDataList)) {
            return Lists.newArrayList();
        }
        return metaMenuDataList.stream().map(x -> {
            MenuDropListItem menuDropListItem = new MenuDropListItem();
            menuDropListItem.setParentId(parentId);
            menuDropListItem.setMetaMenuData(x);
            return menuDropListItem.buildDropListItem();
        }).collect(Collectors.toList());
    }

    @Override
    protected StandGetDropList.Result after(GetMenuDropList.Arg arg, StandGetDropList.Result result) {
        List<DropListItem> dropListItemList = super.after(arg, result).getDropListItemList();
        List<DropListItem> groupDropListItemList = Lists.newArrayList();
        Map<String, List<DropListItem>> menuDropListItemMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(dropListItemList)) {
            log.error("StandGetDropList is  null  , arg= {}", JSONObject.toJSONString(arg));
            return result;
        }
        for (DropListItem dropListItem : dropListItemList) {
            if (MenuType.GROUP.equals(dropListItem.getType())) {
                groupDropListItemList.add(dropListItem);
            } else {
                List<DropListItem> childDropListItems = menuDropListItemMap.getOrDefault(dropListItem.getParentId(), Lists.newArrayList());
                childDropListItems.add(dropListItem);
                menuDropListItemMap.put(dropListItem.getParentId(), childDropListItems);
            }
        }
        List<DropListItem> resultDropListItems = Lists.newArrayList();
        for (DropListItem dropListItem : groupDropListItemList) {
            resultDropListItems.add(dropListItem);
            List<DropListItem> childDropListItems = menuDropListItemMap.getOrDefault(dropListItem.getId(), Lists.newArrayList());
            resultDropListItems.addAll(childDropListItems);
        }
        return StandGetDropList.Result.builder().
                dropListItemList(resultDropListItems).
                build();
    }

    private String getBizIdWithUserInfo(UserInfo userInfo, GetMenuDropList.Arg arg) {
        String bizId = arg.getBizId();
        // 新版crm或者crm迁移预览的时候  appId转为paasCRM
        if (StringUtils.equals(bizId, Constant.APP_CRM) && (checkService.checkGoNewCRM(userInfo.getEnterpriseId()) || (arg.isPreviewNewCrmFlag()))) {
            return Constant.paasCRM;
        }
        return bizId;
    }

    @Override
    protected String getBizId(GetMenuDropList.Arg arg) {
        return arg.getBizId();
    }
}
