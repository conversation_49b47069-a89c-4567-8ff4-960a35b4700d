package com.facishare.webpage.customer.model;

import com.facishare.webpage.customer.api.model.PaaSAppVO;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/7/30
 * @Description : 扩展SimpleLinkAppResult，添加PaaSApp信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SimpleLinkAppResultDto extends SimpleLinkAppResult {
    private PaaSAppVO paaSAppVO;

    public SimpleLinkAppResultDto(PaaSAppVO paaSAppVO) {
        this.paaSAppVO = paaSAppVO;
    }

    public static SimpleLinkAppResult convertToSimpleLinkAppResult(SimpleLinkAppResultDto dto){
        if(Objects.isNull(dto)){
            return null;
        }
        SimpleLinkAppResult simpleLinkAppResult = new SimpleLinkAppResult();
        simpleLinkAppResult.setAppId(dto.getAppId());
        simpleLinkAppResult.setAppName(dto.getAppName());
        simpleLinkAppResult.setAppType(dto.getAppType());
        simpleLinkAppResult.setIcon(dto.getIcon());
        simpleLinkAppResult.setIsExprired(dto.getIsExprired());
        return simpleLinkAppResult;
    }

    /**
     * 从SimpleLinkAppResult构建SimpleLinkAppResultDto
     */
    public static SimpleLinkAppResultDto from(SimpleLinkAppResult source, PaaSAppVO paaSAppVO) {
        SimpleLinkAppResultDto dto = new SimpleLinkAppResultDto();
        // 复制父类属性
        dto.setAppId(source.getAppId());
        dto.setAppName(source.getAppName());
        dto.setIcon(source.getIcon());
        dto.setIsExprired(source.getIsExprired());
        // 设置扩展属性
        dto.setPaaSAppVO(paaSAppVO);
        return dto;
    }
}
