package com.facishare.webpage.customer.config;

import com.alibaba.fastjson.JSON;
import com.facishare.webpage.customer.config.model.DataSource;
import com.facishare.webpage.customer.migrate.config.CrmMigrateConfigManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.admin.ConfigAdminClient;
import com.github.autoconf.admin.api.IConfigAdmin;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.github.jedis.lock.JedisLock;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu
 * @date 2021/12/9 10:50 上午
 */
@Component
@Slf4j
public class UIPaaSConfig {
    private static final IConfigAdmin iConfigAdmin = new ConfigAdminClient();
    private static final String TOKEN = "D3C9C03E32AA68753AD61C2711AC430D5C87AE01B10AF409D9BC9Ffsuipaasconfig";
    private static final String uipaasConfigname = "fs-ui-paas-config.ini";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Resource
    private MergeJedisCmd webpageRedis;
    @Autowired
    private CrmMigrateConfigManager crmMigrateConfigManager;

    // 互联自定义菜单可选应用的配置, CROSS_PAAS 表示自定义互联应用, 其他为应用id, 注意:线上线下互联应用id可能不一致
    private List<String> crossCustomerMenuNoSupportApps = Lists.newArrayList();
    @Getter
    private Map<Integer, Set<String>> crossMenuSupportAppMap = Maps.newHashMap();

    private List<Integer> synNewCrmEIList = Lists.newArrayList();

    private List<String> useV3DropListAppIds = Lists.newArrayList();

    private List<String> customDropAppId = Lists.newArrayList();
    private List<String> specialPageDropAppId = Lists.newArrayList();

    private List<Integer> goNewCrmEnterpriseIdList = Lists.newArrayList();

    private List<DataSource> dataSources = Lists.newArrayList();



    private boolean openNewCrmSwitch;

    private List<Integer> brushEiList = Lists.newArrayList();

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig(uipaasConfigname, new IniChangeListener("crossMenu") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadCrossMenu(iniConfig);
            }
        });

        ConfigFactory.getConfig(uipaasConfigname, new IniChangeListener("dropList") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadDropList(iniConfig);
            }
        });
        ConfigFactory.getConfig(uipaasConfigname, new IniChangeListener("webpage") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadWebPageList(iniConfig);
            }
        });
        ConfigFactory.getConfig("variables_crm_migrate_tenants", this::reloadMigrateTenants);
        ConfigFactory.getConfig(uipaasConfigname, new IniChangeListener("synNewCrmEIs") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadSynNewCrmEIList(iniConfig);
            }
        });

        ConfigFactory.getConfig(uipaasConfigname, new IniChangeListener("website") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                reloadWebSiteConf(iniConfig);
            }
        });

    }
    private void reloadSynNewCrmEIList(IniConfig iniConfig) {
        String reloadSynNewCrmEIs = iniConfig.get("synNewCrmEIs", "");
        String[] split = reloadSynNewCrmEIs.split(",");
        List<Integer> synNewCrmEIList = Lists.newArrayList();
        for (String appId : split) {
            if (StringUtils.isEmpty(appId)) {
                continue;
            }
            synNewCrmEIList.add(Integer.valueOf(appId.trim()));
        }
        this.synNewCrmEIList = synNewCrmEIList;
        log.info("reloadSynNewCrmEIList synNewCrmEIList:{}", this.synNewCrmEIList);
    }

    private void reloadWebPageList(IniConfig iniConfig) {
        boolean openNewCrmSwitch = iniConfig.getBool("openNewCrmSwitch");
        this.openNewCrmSwitch = openNewCrmSwitch;

        List<Integer> eiList = Lists.newArrayList();
        String brushEis = iniConfig.get("brushEiList");
        if (StringUtils.isNotBlank(brushEis)) {
            String[] split = brushEis.split(",");
            for (String ei : split) {
                try {
                    eiList.add(Integer.valueOf(ei));
                } catch (NumberFormatException e) {
                    log.warn("invalid ei:{}", ei);
                }
            }
        }
        this.brushEiList = eiList;
        log.info("reloadWebPageList openNewCrmSwitch:{}, brushEiList:{}", openNewCrmSwitch, brushEis);
    }

    private void reloadCrossMenu(IniConfig iniConfig) {
        try {
            String crossCustomerMenuSupportApp = iniConfig.get("crossCustomerMenuNoSupportApp");
            String[] split = crossCustomerMenuSupportApp.split(",");
            List<String> crossCustomerMenuNoSupportApps = Lists.newArrayList();
            for (String appId : split) {
                if (StringUtils.isEmpty(appId)) {
                    continue;
                }
                crossCustomerMenuNoSupportApps.add(appId.trim());
            }
            this.crossCustomerMenuNoSupportApps = crossCustomerMenuNoSupportApps;
            log.info("reloadCrossMenu crossCustomerMenuNoSupportApps:{}", this.crossCustomerMenuNoSupportApps);
        } catch (Exception e){
            log.warn("reloadCrossMenu crossCustomerMenuNoSupportApps failed:{}", iniConfig.get("crossCustomerMenuNoSupportApp"), e);
        }


        try {
            String crossMenuSupportAppMapStr = iniConfig.get("crossMenuSupportAppMap");
            crossMenuSupportAppMap = OBJECT_MAPPER.readValue(
                    crossMenuSupportAppMapStr, new TypeReference<Map<Integer, Set<String>>>() {}
            );
            log.info("reload crossMenuSupportAppMap success:{}", crossMenuSupportAppMap);
        } catch (Exception e){
            log.warn("reload crossMenuSupportAppMap failed:{}", iniConfig.get("crossMenuSupportAppMap"), e);
        }
    }

    private void reloadDropList(IniConfig iniConfig) {
        String useV3DropList = iniConfig.get("useV3DropList");
        String[] split = useV3DropList.split(",");
        List<String> useV3DropListAppIds = Lists.newArrayList();
        for (String appId : split) {
            if (StringUtils.isEmpty(appId)) {
                continue;
            }
            useV3DropListAppIds.add(appId.trim());
        }
        this.useV3DropListAppIds = useV3DropListAppIds;
        log.info("reloadDropList useV3DropListAppIds:{}", this.useV3DropListAppIds);
    }

    private void reloadMigrateTenants(IConfig config) {
        String goNewCrmEnterpriseIds = config.get("goNewCrmEnterpriseIds");
        if (StringUtils.isEmpty(goNewCrmEnterpriseIds)) {
            return;
        }
        String[] goNewCrmEnterpriseIdsStr = goNewCrmEnterpriseIds.split(",");
        List<Integer> goNewCrmEnterpriseIdList = Lists.newArrayList();
        for (String enterpriseId : goNewCrmEnterpriseIdsStr) {
            if (StringUtils.isEmpty(enterpriseId)) {
                continue;
            }
            goNewCrmEnterpriseIdList.add(Integer.parseInt(enterpriseId.trim()));
        }
        this.goNewCrmEnterpriseIdList = goNewCrmEnterpriseIdList;
        log.info("init reloadMigrateTenants goNewCrmEnterpriseIdList:{}", this.goNewCrmEnterpriseIdList);
    }

    private void reloadWebSiteConf(IniConfig iniConfig) {
        String dataSourcesConfig = iniConfig.get("dataSources", "");
        List<DataSource> newDataSources;
        try {
            if (StringUtils.isBlank(dataSourcesConfig)){
                return;
            }
            newDataSources = JSON.parseArray(dataSourcesConfig, DataSource.class);
        } catch (Exception e) {
            log.warn("reloadWebSiteConf: Failed to parse JSON format. Config: {}", dataSourcesConfig, e);
            return;
        }
        this.dataSources = newDataSources;
        log.info("reloadWebSiteConf dataSources:{}", this.dataSources);
    }

    public List<String> getCrossCustomerMenuNoSupportApps() {
        return Collections.unmodifiableList(crossCustomerMenuNoSupportApps);
    }

    public List<Integer> getSynNewCrmEIList() {
        return Collections.unmodifiableList(synNewCrmEIList);
    }

    public boolean checkUseV3DropList(String appId) {
        return useV3DropListAppIds.contains(appId);
    }


    public boolean checkGoNewCrm(int enterpriseId) {
        return goNewCrmEnterpriseIdList.contains(enterpriseId);
    }

    public boolean openNewCrmSwitch() {
        return openNewCrmSwitch;
    }

    public List<Integer> getBrushEiList() {
        return brushEiList;
    }

    public List<DataSource> getDataSources() {
        return org.apache.commons.collections4.CollectionUtils.emptyIfNull(dataSources).stream()
                .filter(Objects::nonNull)
                .map(DataSource::copy)
                .collect(Collectors.toList());
    }

    public void updateSynNewCrmEIs(String tenantId, boolean addFlag) {
        log.info("updateSynNewCrmEIs===start, model={},Info={}", "synNewCrmEIs", tenantId);
        int retry = 0;
        try (JedisLock jedisLock = new JedisLock(webpageRedis, uipaasConfigname, 600000)) {
            while (retry < 200) {
                retry++;
                if (jedisLock.tryLock()) {
                    StringBuilder sb = new StringBuilder();
                    if (addFlag) {
                        appendKeyValue(sb, StringUtils.join(synNewCrmEIList, ","), ",", tenantId);
                    } else {
                        removeKeyValue(sb, synNewCrmEIList, ",", tenantId);
                    }
                    Boolean result = iConfigAdmin.update(TOKEN, crmMigrateConfigManager.getprofile(), uipaasConfigname, "synNewCrmEIs", sb.toString());
                    jedisLock.unlock();
                    if (!result) {
                        continue;
                    }
                    break;
                }
                Thread.sleep(50);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("updateInfo===end, model={},Info={}", "synNewCrmEIs", tenantId);
    }

    private void appendKeyValue(StringBuilder sb, String oldValue, String split, String addValue) {
        sb.append(oldValue + split + addValue);
        sb.append("\n");
    }

    private void removeKeyValue(StringBuilder sb, List<Integer> oldValue, String split, String removeValue) {
        if (CollectionUtils.isEmpty(oldValue)) {
            return;
        }
        oldValue = oldValue.stream().distinct().collect(Collectors.toList());
        if (oldValue.contains(Integer.valueOf(removeValue))) {
            oldValue.remove(Integer.valueOf(removeValue));
        }
        sb.append(StringUtils.join(oldValue, split));
        sb.append("\n");
    }
}
