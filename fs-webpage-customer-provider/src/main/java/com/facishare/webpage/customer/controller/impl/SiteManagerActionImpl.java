package com.facishare.webpage.customer.controller.impl;

import com.facishare.cep.plugin.annotation.FSClientInfo;
import com.facishare.cep.plugin.annotation.FSUserInfo;
import com.facishare.cep.plugin.model.ClientInfo;
import com.facishare.cep.plugin.model.UserInfo;
import com.facishare.webpage.customer.api.model.User;
import com.facishare.webpage.customer.controller.SiteManagerAction;
import com.facishare.webpage.customer.controller.model.arg.portal.*;
import com.facishare.webpage.customer.controller.model.result.CommonResult;
import com.facishare.webpage.customer.controller.model.result.portal.*;
import com.facishare.webpage.customer.dao.entity.SiteDraftEntity;
import com.facishare.webpage.customer.service.SiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * Created by zhouwr on 2024/11/6.
 */
@Controller
@Slf4j
@RequestMapping("/siteManager")
public class SiteManagerActionImpl implements SiteManagerAction {

    @Autowired
    private SiteService siteService;

    @RequestMapping(value = "createSiteInfo", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public CommonResult createSiteInfo(@FSUserInfo UserInfo userInfo,
                                       @FSClientInfo ClientInfo clientInfo,
                                       @RequestBody CreateSiteInfoArg arg) {
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        siteService.createSiteInfo(user, arg.getSiteInfo());
        return CommonResult.successResult();
    }

    @RequestMapping(value = "findSiteInfo", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public FindSiteInfoResult findSiteInfo(@FSUserInfo UserInfo userInfo,
                                           @FSClientInfo ClientInfo clientInfo,
                                           @RequestBody FindSiteInfoArg arg) {
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        SiteInfoVO siteInfo = siteService.findSiteInfo(user, arg.getSiteApiName());
        return FindSiteInfoResult.builder()
                .siteInfo(siteInfo)
                .build();
    }

    @RequestMapping(value = "publishSiteDraft", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public SiteDraftResult publishSiteDraft(@FSUserInfo UserInfo userInfo,
                                            @FSClientInfo ClientInfo clientInfo,
                                            @RequestBody PublishSiteDraftArg arg) {
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        SiteDraftEntity siteDraftEntity = siteService.publishSiteDraft(user, arg);
        return SiteDraftResult.builder()
                .siteApiName(siteDraftEntity.getSiteApiName())
                .version(siteDraftEntity.getVersion())
                .build();
    }

    @RequestMapping(value = "updateSiteInfo", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public CommonResult updateSiteInfo(@FSUserInfo UserInfo userInfo,
                                       @FSClientInfo ClientInfo clientInfo,
                                       @RequestBody UpdateSiteInfoArg arg) {
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        siteService.updateSiteInfo(user, arg.getSiteInfo());
        return CommonResult.successResult();
    }

    @RequestMapping(value = "updateSitePages", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public SiteDraftResult updateSitePages(@FSUserInfo UserInfo userInfo,
                                           @FSClientInfo ClientInfo clientInfo,
                                           @RequestBody UpdateSitePagesArg arg) {
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        SiteDraftEntity siteDraftEntity = siteService.updateSitePages(user, arg);
        return SiteDraftResult.builder()
                .siteApiName(siteDraftEntity.getSiteApiName())
                .version(siteDraftEntity.getVersion())
                .build();
    }

    @RequestMapping(value = "findSiteInfosByAppId", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public FindSiteInfosByAppIdResult findSiteInfosByAppId(@FSUserInfo UserInfo userInfo,
                                                           @FSClientInfo ClientInfo clientInfo,
                                                           @RequestBody FindSiteInfosByAppIdArg arg) {
        List<SiteInfoVO> siteInfoVOList = siteService.findSitesByAppId(userInfo.getEnterpriseId(), arg.getAppId());
        return FindSiteInfosByAppIdResult.builder()
                .siteInfoList(siteInfoVOList)
                .build();
    }

    @RequestMapping(value = "findSiteByApiName", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public FindSiteResult findSiteByApiName(@FSUserInfo UserInfo userInfo,
                                            @FSClientInfo ClientInfo clientInfo,
                                            @RequestBody FindSiteByApiNameArg arg) {
        arg.validAndFix();
        return siteService.findSiteByApiNameForManager(arg.getSiteApiName(), arg.getClientType());
    }

    @RequestMapping(value = "getDataSourceType", method = RequestMethod.POST)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseBody
    @Override
    public DataSourceTypeResult getDataSourceType(@FSUserInfo UserInfo userInfo,
                                                  @FSClientInfo ClientInfo clientInfo) {
        User user = User.of(userInfo.getEnterpriseId(), userInfo.getEmployeeId());
        return siteService.getDataSourceType(user, clientInfo.getLocale());
    }
}
