package com.facishare.webpage.customer.service;

import com.facishare.webpage.customer.api.model.RoleInfo;
import com.facishare.webpage.customer.model.CrossAppVO;
import com.facishare.webpage.customer.model.SimpleLinkAppResultDto;
import com.fxiaoke.enterpriserelation2.data.LinkAppPartialData;
import com.fxiaoke.enterpriserelation2.result.LinkAppVo;
import com.fxiaoke.enterpriserelation2.result.SimpleLinkAppResult;
import com.fxiaoke.enterpriserelation2.result.data.LinkAppData;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Created by zhangyu on 2020/6/30
 */
public interface RemoteCrossService {

    List<SimpleLinkAppResultDto> getUpSimpleLinkAppDto(int tenantId, Locale locale);

    List<SimpleLinkAppResult> getUpSimpleLinkApp(int tenantId, Locale locale);
    List<SimpleLinkAppResult> getUpSimpleLinkApp(int tenantId);

    Map<String, CrossAppVO> getCrossAppMap(int upTenantId, String upEnterpriseAccount, long outTenantId, long outUserId);

    List<Integer> getUpTenantIds(int downTenantId);

    List<RoleInfo> getOuterRoleInfosByAppId(int tenantId, String appId);

    List<String> getUserOuterRoleIds(int tenantId, long outTenantId, long outUserId, String appId);

    List<String> getCrossAppIds(int upTenantId, String upEnterpriseAccount, long outTenantId, long outUserId);

    List<LinkAppVo> getLinkAppVo(int upTenantId, String upEnterpriseAccount, long outTenantId, long outUserId, Locale locale);

    /**
     * 获取互联工作台
     *
     * @param enterpriseId
     * @param employeeId   ：-10000 租户级， 正常employeeId为个人级
     * @param locale
     * @return
     */
    List<LinkAppData> getRelationBench(int enterpriseId, int employeeId, Locale locale);

    /**
     * 获取本企业下互联应用IDs对应应用的部分信息
     * @param enterpriseId
     * @param linkAppIds
     * @return
     */
    Map<String, LinkAppPartialData> getLinkAppPartialData(Integer enterpriseId, List<String> linkAppIds);

    /**
     * 获取本企业的所有上游企业的互联应用的部分信息
     *
     * @param enterpriseId
     * @param employeeId
     * @return
     */
    Map<String, Map<String, LinkAppPartialData>> listAllUpstreamOpenedLinkAppPartialDatas(Integer enterpriseId, int employeeId);


}
