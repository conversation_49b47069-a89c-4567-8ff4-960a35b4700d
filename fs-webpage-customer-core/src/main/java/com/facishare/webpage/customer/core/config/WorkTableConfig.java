package com.facishare.webpage.customer.core.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.webpage.customer.api.model.core.TenantPrivilege;
import com.github.autoconf.ConfigFactory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/7/4
 * @Description : 工作台配置类，用于加载 fs-webpage-work-table-config 配置文件
 */
@Slf4j
public class WorkTableConfig {

    private static Map<String, App> appMap = Collections.emptyMap();
    private static Map<String, Channel> channelMap = Collections.emptyMap();
    private static Map<String, Set<String>> appChannels = Collections.emptyMap();  // appId -> 可选渠道分类查询表

    static {
        ConfigFactory.getInstance().getConfig("fs-webpage-work-table-config", iConfig -> {
            try {
                WorkTableConfigData configData = JSONObject.parseObject(iConfig.getString(), WorkTableConfigData.class);
                log.info("work table config data: {}", JSONObject.toJSONString(configData));
                if (configData != null) {
                    configData.nullToEmpty();
                    appMap = configData.getApp().stream()
                            .collect(Collectors.collectingAndThen(
                                    Collectors.toMap(App::getAppId, Function.identity(), (key1, key2) -> key2),
                                    Collections::unmodifiableMap));

                    channelMap = configData.getChannel().stream()
                            .collect(Collectors.collectingAndThen(
                                    Collectors.toMap(Channel::getChannelId, Function.identity(), (key1, key2) -> key2),
                                    Collections::unmodifiableMap));

                    appChannels = configData.getChannel().stream()
                            .flatMap(channel -> CollectionUtils.emptyIfNull(channel.getAppIds()).stream()
                                    .map(val -> new AbstractMap.SimpleEntry<>(val, channel.getChannelId())))
                            .collect(Collectors.collectingAndThen(
                                    Collectors.groupingBy(
                                            AbstractMap.SimpleEntry::getKey,
                                            Collectors.mapping(
                                                    AbstractMap.SimpleEntry::getValue,
                                                    Collectors.collectingAndThen(Collectors.toSet(), Collections::unmodifiableSet)
                                            )
                                    ),
                                    Collections::unmodifiableMap));
                }
            } catch (Exception e){
                log.error("work table config data parse error, {}", iConfig.getString());
            }
        });
    }

    // 通用深拷贝方法
    private static <T> T deepCopy(T object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        return JSONObject.parseObject(JSONObject.toJSONString(object), clazz);
    }

    private static <T> Map<String, T> deepCopyMap(Map<String, T> originalMap, Class<T> valueClass) {
        Map<String, T> result = new HashMap<>();
        for (Map.Entry<String, T> entry : originalMap.entrySet()) {
            result.put(entry.getKey(), deepCopy(entry.getValue(), valueClass));
        }
        return result;
    }

    // 查询方法, 返回数据深拷贝
    public static Map<String, App> getAppMap() {
        return deepCopyMap(appMap, App.class);
    }

    public static Map<String, Channel> getChannelMap() {
        return deepCopyMap(channelMap, Channel.class);
    }

    public static Map<String, Set<String>> getAppChannels() {
        Map<String, Set<String>> result = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : appChannels.entrySet()) {
            result.put(entry.getKey(), new HashSet<>(entry.getValue()));
        }
        return result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkTableConfigData implements Serializable {

        @JSONField(ordinal = 1)
        private List<App> app;

        @JSONField(ordinal = 2)
        private List<Channel> channel;

        public void nullToEmpty(){
            this.app = new ArrayList<>(CollectionUtils.emptyIfNull(this.app));
            this.channel = new ArrayList<>(CollectionUtils.emptyIfNull(this.channel));
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class App implements Serializable {

        @JSONField(ordinal = 1)
        private String appId;

        @JSONField(ordinal = 2)
        private String appName;

        @JSONField(ordinal = 3)
        private String nameI18n;

        @JSONField(ordinal = 4)
        private Integer order = Integer.MAX_VALUE;

        @JSONField(ordinal = 5)
        private TenantPrivilege tenantPrivilege;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Channel implements Serializable {

        @JSONField(ordinal = 1)
        private String channelId;

        @JSONField(ordinal = 2)
        private String channelName;

        @JSONField(ordinal = 3)
        private String nameI18n;

        @JSONField(ordinal = 4)
        private List<String> appIds = Collections.emptyList();

        @JSONField(ordinal = 5)
        private Integer order = Integer.MAX_VALUE;

        @JSONField(ordinal = 6)
        private TenantPrivilege tenantPrivilege;
    }

    @AllArgsConstructor
    @Data
    public static class AppAndChannelMaps {
        private final Map<String, App> appMap;
        private final Map<String, Channel> channelMap;
    }
}
