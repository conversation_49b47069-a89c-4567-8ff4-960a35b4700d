package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.model.core.Menu;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 19/12/20.
 */
public interface QueryMenus {

    @Data
    class Arg implements Serializable {
        private int tenantId;
        private List<String> menuIds;
    }

    @Data
    class Result implements Serializable {
        private List<Menu> menus;
    }
}
