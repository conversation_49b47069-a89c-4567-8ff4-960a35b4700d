package com.facishare.webpage.customer.api.model.arg;

import com.facishare.webpage.customer.api.model.core.Menu;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by she<PERSON> on 19/12/20.
 */
public interface QueryMenusByCollectionIds {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg implements Serializable{
        private int tenantId;
        private List<String> collectionIds;
    }

    @Data
    class Result implements Serializable {

        private Map<String,List<Menu>> menus;
    }

}
