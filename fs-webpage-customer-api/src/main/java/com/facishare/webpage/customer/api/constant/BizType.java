package com.facishare.webpage.customer.api.constant;

import com.facishare.webpage.customer.api.InterErrorCode;
import com.facishare.webpage.customer.api.exception.WebPageException;
import com.facishare.webpage.customer.api.utils.WebPageUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/3/10
 * 备注：增加枚举，需要维护以下方法
 *
 * @see BizType#getAppType(java.lang.String)
 * @Description 描述页面的枚举(区别于AppTypeEnum)
 */
@Getter
public enum BizType {
    DEFAULT(-1, "", "", 1),
    /**
     * CRM
     */
    CRM(1, "CRM", "CRM", 1),
    /**
     * 应用
     */
    APP(2, "APP", "", 1),
    /**
     * BI
     */
    BI(3, "BI", "", 2),
    /**
     * 自定义页面
     */
    CUSTOMER(4, "CUSTOMER", "PortalPage", 1),
    /**
     * 互联自定义页面(实际互联自定义页面请求是取值7, 跟随自定义互联应用, 也就是 CROSS_PaaS)
     */
    CROSSCUSTOMER(4,"CUSTOMER","CrossPortalPage",1),
    /**
     * PaaS应用
     */
    PAAS(5, "PAAS", "PaaS", 1),
    /**
     * 预置自定义页面
     */
    PRE_CUSTOMER(6, "PRE_CUSTOMER", "", 1),
    /**
     * 自定义互联应用
     */
    CUSTOMER_LINK_APP(7, "CUSTOMER_LINK_APP", "CROSS_PaaS", 1),
    /**
     * 预置互联应用
     */
    LINK_APP(8, "LINK_APP", "", 1),
    SHOPPING_PRESET(9, "SHOPPING_PRESET", "", 1),
    /*
     * 独立站点页面：标准页面
     */
    WEBSITE(10, "WEBSITE", "Website",1),
    /*
     * 独立站点页面: 购物车
     */
    WEBSITE_SHOPPING(11, "WEBSITE", "Website_Shopping",1),
    /*
     * 独立站点页面: 对象列表
     */
    WEBSITE_OBJECT(12, "WEBSITE", "Website_ObjectList",1),
    /*
     * 独立站点页面: 对象详情
     */
    WEBSITE_OBJECT_DETAIL(13, "WEBSITE", "Website_ObjectDetail",1),
    /*
     * 独立站点页面: 对象新建
     */
    WEBSITE_OBJECT_ADD(14, "WEBSITE", "Website_ObjectAdd",1),
    /*
     * 独立站点页面: 对象编辑
     */
    WEBSITE_OBJECT_EDIT(15, "WEBSITE", "Website_ObjectEdit",1),
    /*
     * 客服工作台侧边栏布局
     */
    SIDEBAR(100, "WorkTableSidebar", "WorkTableSidebar", 1)
    ;

    private final int type;

    private final String value;

    private final String defaultAppId;

    private final int pageTemplateType;

    BizType(int type, String value, String defaultAppId, int pageTemplateType) {
        this.type = type;
        this.value = value;
        this.defaultAppId = defaultAppId;
        this.pageTemplateType = pageTemplateType;
    }

    public static BizType getBizTypeValue(int type) {
        BizType[] values = BizType.values();

        for (BizType bizType : values) {
            if (bizType.type == type) {
                return bizType;
            }
        }
        return null;
    }


    public static BizType getBusinessType(String appId) {
        if (Constant.APP_CRM.equals(appId)) {
            return CRM;
        }
        if (CUSTOMER.getDefaultAppId().equals(appId)) {
            return CUSTOMER;
        }
        if ("multiCrossPortal".equals(appId) || WebPageUtils.checkPaaSApp(appId)) {
            return PAAS;
        }
        return APP;
    }

    public static int getAppType(String appId) {
        if (StringUtils.isEmpty(appId)) {
            throw new WebPageException(InterErrorCode.FS_WEBPAGE_CUSTOMER_PARAMETER_ERROR);
        }
        if (CRM.getDefaultAppId().equals(appId)) {
            return CRM.getType();
        }
        if (CUSTOMER.getDefaultAppId().equals(appId)) {
            return CUSTOMER.getType();
        }
        if (WebPageUtils.checkPaaSApp(appId)) {
            return PAAS.getType();
        }
        return APP.getType();
    }



    public static boolean isWebsite(int bizType){
        BizType bizTypeValue = getBizTypeValue(bizType);
        if (bizTypeValue != null) {
            return bizTypeValue.getValue().equals(WEBSITE.getValue());
        }
        return false;
    }

    public static boolean isSpecialWebsite(int bizType){
        return isWebsite(bizType) && bizType != WEBSITE.getType();
    }

    public static boolean isSinglePage(int bizType){
        return BizType.CUSTOMER.getType() == bizType
                || BizType.CROSSCUSTOMER.getType() == bizType
                || BizType.SIDEBAR.getType() == bizType;
    }

}
